import mysql.connector
from mysql.connector import Error
from sqlalchemy import create_engine, text
import pandas as pd

def create_database_connection():
    """MySQL veritabanı bağlantısı oluşturur"""
    try:
        # Veritabanı bağlantı bilgileri (kullanıcı tarafından güncellenecek)
        config = {
            'host': 'localhost',
            'user': 'root',  # MySQL kullanıcı adınız
            'password': '',  # MySQL şifreniz
            'database': 'sinav_programi',
            'charset': 'utf8mb4'
        }
        
        # Önce veritabanını oluştur
        connection = mysql.connector.connect(
            host=config['host'],
            user=config['user'],
            password=config['password']
        )
        
        cursor = connection.cursor()
        cursor.execute("CREATE DATABASE IF NOT EXISTS sinav_programi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        cursor.close()
        connection.close()
        
        # Şimdi veritabanına bağlan
        connection = mysql.connector.connect(**config)
        print("✅ MySQL veritabanı bağlantısı başarılı!")
        return connection
        
    except Error as e:
        print(f"❌ Veritabanı bağlantı hatası: {e}")
        return None

def create_tables():
    """Veritabanı tablolarını oluşturur"""
    
    connection = create_database_connection()
    if not connection:
        return False
    
    cursor = connection.cursor()
    
    try:
        # 1. dersler tablosu
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS dersler (
            id INT AUTO_INCREMENT PRIMARY KEY,
            kod VARCHAR(20) NOT NULL UNIQUE,
            ad VARCHAR(100) NOT NULL,
            akts INT DEFAULT 0,
            bolum VARCHAR(50) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ 'dersler' tablosu oluşturuldu")
        
        # 2. sinavlar tablosu
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS sinavlar (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ders_kodu VARCHAR(20) NOT NULL,
            ogretim_uyesi VARCHAR(100) NOT NULL,
            ogrenci_sayisi INT NOT NULL,
            sure INT NOT NULL COMMENT 'Dakika cinsinden',
            bilgisayar_gerekli BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (ders_kodu) REFERENCES dersler(kod) ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ 'sinavlar' tablosu oluşturuldu")
        
        # 3. sinav_tercihleri tablosu
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS sinav_tercihleri (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sinav_id INT NOT NULL,
            tercih1 DATE,
            tercih2 DATE,
            tercih3 DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sinav_id) REFERENCES sinavlar(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ 'sinav_tercihleri' tablosu oluşturuldu")
        
        # 4. derslikler tablosu
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS derslikler (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ad VARCHAR(50) NOT NULL UNIQUE,
            kapasite INT NOT NULL,
            bolum VARCHAR(50) NOT NULL,
            bilgisayar_var_mi BOOLEAN DEFAULT FALSE,
            aktif BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ 'derslikler' tablosu oluşturuldu")
        
        # 5. ayarlar tablosu
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS ayarlar (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sinav_baslangic_tarihi DATE NOT NULL,
            sinav_bitis_tarihi DATE NOT NULL,
            gunluk_sinav_saatleri JSON COMMENT 'Günlük sınav saatleri',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ 'ayarlar' tablosu oluşturuldu")
        
        # Örnek veriler ekle
        insert_sample_data(cursor)
        
        connection.commit()
        print("\n🎉 Tüm tablolar başarıyla oluşturuldu!")
        return True
        
    except Error as e:
        print(f"❌ Tablo oluşturma hatası: {e}")
        connection.rollback()
        return False
        
    finally:
        cursor.close()
        connection.close()

def insert_sample_data(cursor):
    """Örnek verileri ekler"""
    
    # Örnek dersler
    dersler_data = [
        ('MAT101', 'Matematik I', 6, 'Mühendislik'),
        ('FIZ201', 'Fizik II', 5, 'Mühendislik'),
        ('BIL301', 'Bilgisayar Programlama', 4, 'Bilgisayar Müh.'),
        ('PRJ401', 'Bitirme Projesi', 8, 'Mühendislik'),
        ('KIM101', 'Genel Kimya', 5, 'Mühendislik')
    ]
    
    cursor.executemany("""
        INSERT IGNORE INTO dersler (kod, ad, akts, bolum) 
        VALUES (%s, %s, %s, %s)
    """, dersler_data)
    
    # Örnek derslikler
    derslikler_data = [
        ('A101', 150, 'Mühendislik', False),
        ('A102', 120, 'Mühendislik', False),
        ('A103', 100, 'Mühendislik', False),
        ('B201', 80, 'Mühendislik', False),
        ('B202', 60, 'Mühendislik', False),
        ('C301', 40, 'Bilgisayar Müh.', True),
        ('C302', 35, 'Bilgisayar Müh.', True),
        ('LAB1', 30, 'Bilgisayar Müh.', True),
        ('LAB2', 25, 'Bilgisayar Müh.', True)
    ]
    
    cursor.executemany("""
        INSERT IGNORE INTO derslikler (ad, kapasite, bolum, bilgisayar_var_mi) 
        VALUES (%s, %s, %s, %s)
    """, derslikler_data)
    
    # Örnek ayarlar
    cursor.execute("""
        INSERT IGNORE INTO ayarlar (sinav_baslangic_tarihi, sinav_bitis_tarihi, gunluk_sinav_saatleri) 
        VALUES ('2024-01-15', '2024-01-30', '["09:00", "11:00", "14:00", "16:00"]')
    """)
    
    print("✅ Örnek veriler eklendi")

def show_tables_info():
    """Oluşturulan tabloların bilgilerini gösterir"""
    connection = create_database_connection()
    if not connection:
        return
    
    cursor = connection.cursor()
    
    try:
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        print("\n📋 Oluşturulan tablolar:")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  • {table_name}: {count} kayıt")
            
    except Error as e:
        print(f"❌ Tablo bilgisi alma hatası: {e}")
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    print("🗄️  MySQL Veritabanı Tabloları Oluşturuluyor...")
    print("⚠️  Lütfen MySQL bağlantı bilgilerinizi güncelleyin!")
    
    if create_tables():
        show_tables_info()
    else:
        print("❌ Tablolar oluşturulamadı!")
