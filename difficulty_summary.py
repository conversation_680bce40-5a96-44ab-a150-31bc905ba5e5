import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

def create_difficulty_visualization():
    """Zorluk seviyesi görselleştirmesi oluşturur"""
    
    try:
        # Zorluk eklenmis dosyayı oku
        df = pd.read_excel("sinav_bilgileri_sablon_temizlenmis_zorluk_eklenmis.xlsx")
        
        print("📊 Zorluk seviyesi görselleştirmeleri oluşturuluyor...")
        
        # Stil ayarları
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 2x2 subplot oluştur
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Sınav Zorluk Seviyesi Analizi', fontsize=16, fontweight='bold')
        
        # 1. Z<PERSON><PERSON> dağılımı (pasta grafiği)
        difficulty_counts = df['Zorluk Seviyesi'].value_counts()
        axes[0, 0].pie(difficulty_counts.values, labels=difficulty_counts.index, autopct='%1.1f%%')
        axes[0, 0].set_title('Zorluk Seviyesi Dağılımı')
        
        # 2. Zorluk seviyesine göre ortalama sınav süresi
        avg_duration = df.groupby('Zorluk Seviyesi')['Sınav Süresi'].mean()
        axes[0, 1].bar(avg_duration.index, avg_duration.values)
        axes[0, 1].set_title('Zorluk Seviyesine Göre Ortalama Sınav Süresi')
        axes[0, 1].set_ylabel('Süre (dakika)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. Zorluk seviyesine göre ortalama AKTS
        avg_akts = df.groupby('Zorluk Seviyesi')['akts'].mean()
        axes[1, 0].bar(avg_akts.index, avg_akts.values, color='orange')
        axes[1, 0].set_title('Zorluk Seviyesine Göre Ortalama AKTS')
        axes[1, 0].set_ylabel('AKTS')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 4. Zorluk seviyesine göre ortalama öğrenci sayısı
        avg_students = df.groupby('Zorluk Seviyesi')['Öğrenci Sayısı'].mean()
        axes[1, 1].bar(avg_students.index, avg_students.values, color='green')
        axes[1, 1].set_title('Zorluk Seviyesine Göre Ortalama Öğrenci Sayısı')
        axes[1, 1].set_ylabel('Öğrenci Sayısı')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('zorluk_analizi_grafikleri.png', dpi=300, bbox_inches='tight')
        print("✅ Görselleştirme kaydedildi: zorluk_analizi_grafikleri.png")
        
        return True
        
    except Exception as e:
        print(f"❌ Görselleştirme hatası: {e}")
        return False

def print_difficulty_summary():
    """Zorluk seviyesi özetini yazdırır"""
    
    try:
        df = pd.read_excel("sinav_bilgileri_sablon_temizlenmis_zorluk_eklenmis.xlsx")
        
        print("\n🎯 ZORLUK SEVİYESİ ÖZETİ")
        print("=" * 50)
        
        # Genel istatistikler
        total_exams = len(df)
        print(f"📊 Toplam sınav sayısı: {total_exams}")
        
        # Zorluk dağılımı
        difficulty_dist = df['Zorluk Seviyesi'].value_counts()
        print(f"\n🎯 Zorluk Dağılımı:")
        for difficulty, count in difficulty_dist.items():
            percentage = (count / total_exams) * 100
            print(f"  • {difficulty}: {count} sınav (%{percentage:.1f})")
        
        # En zor ve en kolay sınavlar
        print(f"\n🔥 En Zor Sınavlar:")
        hard_exams = df[df['Zorluk Seviyesi'] == 'Çok Zor']
        for _, exam in hard_exams.iterrows():
            print(f"  • {exam['Ders Kodu']}: {exam['akts']} AKTS, {exam['Sınav Süresi']} dk")
        
        if 'Kolay' in difficulty_dist.index:
            print(f"\n😊 En Kolay Sınavlar:")
            easy_exams = df[df['Zorluk Seviyesi'] == 'Kolay']
            for _, exam in easy_exams.iterrows():
                print(f"  • {exam['Ders Kodu']}: {exam['akts']} AKTS, {exam['Sınav Süresi']} dk")
        
        # Ortalama değerler
        print(f"\n📈 Ortalama Değerler:")
        print(f"  • AKTS: {df['akts'].mean():.1f}")
        print(f"  • Sınav Süresi: {df['Sınav Süresi'].mean():.1f} dakika")
        print(f"  • Öğrenci Sayısı: {df['Öğrenci Sayısı'].mean():.1f}")
        
        # Zorluk puanı dağılımı
        print(f"\n🎯 Zorluk Puanı Dağılımı:")
        score_dist = df['Zorluk Puanı'].value_counts().sort_index()
        for score, count in score_dist.items():
            difficulty_name = df[df['Zorluk Puanı'] == score]['Zorluk Seviyesi'].iloc[0]
            print(f"  • Puan {score} ({difficulty_name}): {count} sınav")
        
        return True
        
    except Exception as e:
        print(f"❌ Özet oluşturma hatası: {e}")
        return False

if __name__ == "__main__":
    print("📊 Zorluk seviyesi özet raporu oluşturuluyor...")
    
    # Özet yazdır
    if print_difficulty_summary():
        print("\n✅ Özet raporu tamamlandı")
    
    # Görselleştirme oluştur
    if create_difficulty_visualization():
        print("✅ Görselleştirme tamamlandı")
    else:
        print("⚠️ Görselleştirme oluşturulamadı (matplotlib gerekli)")
