import pandas as pd
from datetime import datetime, time
from exam_constraints import ExamConstraints

def create_test_schedule():
    """Test için örnek sınav programı oluşturur"""
    
    # Test senaryoları
    test_data = {
        'Ders Kodu': ['MAT101', 'FIZ201', 'BIL301', 'PRJ401', 'KIM101'],
        '<PERSON><PERSON><PERSON><PERSON><PERSON> Sevi<PERSON>': ['1. Sınıf', '2. Sınıf', '3. Sını<PERSON>', '4. Sınıf', '1. Sınıf'],
        'Zorluk Seviyesi': ['<PERSON>or', '<PERSON><PERSON> Zor', '<PERSON><PERSON>', '<PERSON><PERSON>or', '<PERSON><PERSON>'],
        '<PERSON><PERSON><PERSON><PERSON>': [120, 85, 45, 30, 110],
        'Sınav Süresi': [90, 120, 90, 180, 90],
        '<PERSON><PERSON><PERSON><PERSON><PERSON> Gerekli': [False, False, True, True, False],
        'Tarih': ['2024-01-15', '2024-01-15', '2024-01-16', '2024-01-17', '2024-01-15'],
        'Saat': ['09:00:00', '11:00:00', '14:00:00', '16:00:00', '09:00:00'],
        'Derslik': ['A101', 'A102', 'LAB1', 'LAB2', 'A103']
    }
    
    return pd.DataFrame(test_data)

def create_test_classrooms():
    """Test için örnek derslik bilgileri oluşturur"""
    
    classroom_data = {
        'ad': ['A101', 'A102', 'A103', 'B201', 'LAB1', 'LAB2'],
        'kapasite': [150, 120, 100, 80, 40, 35],
        'bilgisayar_var_mi': [False, False, False, False, True, True],
        'bolum': ['Mühendislik', 'Mühendislik', 'Mühendislik', 'Mühendislik', 'Bilgisayar', 'Bilgisayar']
    }
    
    return pd.DataFrame(classroom_data)

def test_constraint_violations():
    """Kısıt ihlallerini test eder"""
    
    print("🧪 KISIT İHLAL TESTLERİ")
    print("=" * 50)
    
    constraints = ExamConstraints()
    
    # Test 1: Geçerli program
    print("\n📋 Test 1: Geçerli Program")
    valid_schedule = create_test_schedule()
    classrooms = create_test_classrooms()
    
    results = constraints.validate_all_constraints(valid_schedule, classrooms)
    print(f"Toplam ihlal: {results['total_violations']}")
    if results['violations']:
        for violation in results['violations']:
            print(f"  ❌ {violation}")
    else:
        print("  ✅ Hiç ihlal yok!")
    
    # Test 2: Aynı sınıf seviyesi çakışması
    print("\n📋 Test 2: Aynı Sınıf Seviyesi Çakışması")
    conflict_schedule = valid_schedule.copy()
    conflict_schedule.loc[4, 'Sınıf Seviyesi'] = '1. Sınıf'  # MAT101 ile aynı seviye
    conflict_schedule.loc[4, 'Saat'] = '09:00:00'  # Aynı saat
    conflict_schedule.loc[4, 'Tarih'] = '2024-01-15'  # Aynı tarih
    
    results = constraints.validate_all_constraints(conflict_schedule, classrooms)
    print(f"Toplam ihlal: {results['total_violations']}")
    for violation in results['violations']:
        print(f"  ❌ {violation}")
    
    # Test 3: Aynı gün iki zor sınav
    print("\n📋 Test 3: Aynı Gün İki Zor Sınav")
    difficult_schedule = valid_schedule.copy()
    difficult_schedule.loc[1, 'Tarih'] = '2024-01-15'  # FIZ201'i aynı güne al
    
    results = constraints.validate_all_constraints(difficult_schedule, classrooms)
    print(f"Toplam ihlal: {results['total_violations']}")
    for violation in results['violations']:
        print(f"  ❌ {violation}")
    
    # Test 4: Öğle arası yasağı
    print("\n📋 Test 4: Öğle Arası Yasağı")
    lunch_schedule = valid_schedule.copy()
    lunch_schedule.loc[0, 'Saat'] = '12:30:00'  # Yasak saatte
    
    results = constraints.validate_all_constraints(lunch_schedule, classrooms)
    print(f"Toplam ihlal: {results['total_violations']}")
    for violation in results['violations']:
        print(f"  ❌ {violation}")
    
    # Test 5: Cuma namaz yasağı
    print("\n📋 Test 5: Cuma Namaz Yasağı")
    friday_schedule = valid_schedule.copy()
    friday_schedule.loc[0, 'Tarih'] = '2024-01-19'  # Cuma
    friday_schedule.loc[0, 'Saat'] = '12:30:00'  # Yasak saatte
    
    results = constraints.validate_all_constraints(friday_schedule, classrooms)
    print(f"Toplam ihlal: {results['total_violations']}")
    for violation in results['violations']:
        print(f"  ❌ {violation}")
    
    # Test 6: Bilgisayar derslik hatası
    print("\n📋 Test 6: Bilgisayar Derslik Hatası")
    computer_schedule = valid_schedule.copy()
    computer_schedule.loc[2, 'Derslik'] = 'A101'  # Bilgisayarsız derslik
    
    results = constraints.validate_all_constraints(computer_schedule, classrooms)
    print(f"Toplam ihlal: {results['total_violations']}")
    for violation in results['violations']:
        print(f"  ❌ {violation}")
    
    # Test 7: Kapasite aşımı
    print("\n📋 Test 7: Kapasite Aşımı")
    capacity_schedule = valid_schedule.copy()
    capacity_schedule.loc[0, 'Öğrenci Sayısı'] = 200  # A101 kapasitesi 150
    
    results = constraints.validate_all_constraints(capacity_schedule, classrooms)
    print(f"Toplam ihlal: {results['total_violations']}")
    for violation in results['violations']:
        print(f"  ❌ {violation}")
    
    # Test 8: Sınav haftası dışı
    print("\n📋 Test 8: Sınav Haftası Dışı")
    week_schedule = valid_schedule.copy()
    week_schedule.loc[0, 'Tarih'] = '2024-02-15'  # Sınav haftası dışı
    
    results = constraints.validate_all_constraints(week_schedule, classrooms, "2024-01-15", "2024-01-30")
    print(f"Toplam ihlal: {results['total_violations']}")
    for violation in results['violations']:
        print(f"  ❌ {violation}")

def test_valid_time_slots():
    """Geçerli saat dilimlerini test eder"""
    
    print("\n🕐 GEÇERLİ SAAT DİLİMLERİ TESTİ")
    print("=" * 40)
    
    constraints = ExamConstraints()
    
    # Normal gün (Pazartesi)
    monday = pd.Timestamp('2024-01-15')  # Pazartesi
    valid_slots_monday = constraints.get_valid_time_slots(monday)
    print(f"Pazartesi geçerli saatler: {[str(t) for t in valid_slots_monday]}")
    
    # Cuma günü
    friday = pd.Timestamp('2024-01-19')  # Cuma
    valid_slots_friday = constraints.get_valid_time_slots(friday)
    print(f"Cuma geçerli saatler: {[str(t) for t in valid_slots_friday]}")

def generate_constraint_report():
    """Kısıt raporu oluşturur"""
    
    report = []
    report.append("SINAV PLANLAMA KISITLARI RAPORU")
    report.append("=" * 60)
    report.append(f"Oluşturulma Tarihi: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    report.append("TANIMLI KISITLAR:")
    report.append("1. Aynı sınıf seviyesi dersleri aynı saatte sınav olamaz")
    report.append("2. Aynı gün iki zor/çok zor sınav yapılamaz")
    report.append("3. Aynı gün birden fazla sınav varsa aralarında 15 dakika boşluk")
    report.append("4. 12:15–13:00 arası sınav yapılamaz (öğle arası)")
    report.append("5. Cuma 12:00–13:30 arası sınav yapılamaz (namaz saati)")
    report.append("6. Bilgisayar gerekiyorsa sadece bilgisayarlı derslik kullan")
    report.append("7. Sınav haftası dışında tarih atanamaz")
    report.append("8. Derslik kapasitesi yeterli olmalı (ek kısıt)")
    report.append("")
    
    report.append("SINAV SAATLERİ:")
    report.append("• 09:00 - Sabah sınavı")
    report.append("• 11:00 - Öğleden önce sınavı")
    report.append("• 14:00 - Öğleden sonra sınavı")
    report.append("• 16:00 - Akşam sınavı")
    report.append("")
    
    report.append("YASAK SAATLER:")
    report.append("• Öğle arası: 12:15-13:00 (tüm günler)")
    report.append("• Cuma namaz: 12:00-13:30 (sadece Cuma)")
    report.append("")
    
    report.append("KISIT KONTROL YÖNTEMLERİ:")
    report.append("• validate_same_class_level_conflict(): Sınıf seviyesi çakışması")
    report.append("• validate_difficult_exam_limit(): Zor sınav limiti")
    report.append("• validate_minimum_break_time(): Minimum boşluk süresi")
    report.append("• validate_lunch_break_forbidden(): Öğle arası yasağı")
    report.append("• validate_friday_prayer_forbidden(): Cuma namaz yasağı")
    report.append("• validate_computer_classroom_required(): Bilgisayar derslik")
    report.append("• validate_exam_week_only(): Sınav haftası kontrolü")
    report.append("• validate_classroom_capacity(): Kapasite kontrolü")
    
    with open("kisit_raporu.txt", "w", encoding="utf-8") as f:
        f.write("\n".join(report))
    
    print("✅ Kısıt raporu kaydedildi: kisit_raporu.txt")

if __name__ == "__main__":
    # Kısıt testlerini çalıştır
    test_constraint_violations()
    
    # Geçerli saat dilimlerini test et
    test_valid_time_slots()
    
    # Rapor oluştur
    generate_constraint_report()
    
    print("\n✅ Tüm kısıt testleri tamamlandı!")
