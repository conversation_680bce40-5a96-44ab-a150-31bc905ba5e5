import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_excel_template():
    """Excel şablonu oluşturur"""
    
    # Örnek veriler
    data = {
        'Sınıf Seviyesi': [
            '1. <PERSON><PERSON>n<PERSON><PERSON>', '2. <PERSON><PERSON><PERSON><PERSON><PERSON>', '3. <PERSON><PERSON><PERSON><PERSON><PERSON>', '4. <PERSON><PERSON><PERSON><PERSON><PERSON>', 
            '1. <PERSON><PERSON><PERSON><PERSON><PERSON>', '2. <PERSON><PERSON><PERSON><PERSON><PERSON>', '3. <PERSON><PERSON><PERSON><PERSON><PERSON>', '4. <PERSON><PERSON><PERSON><PERSON><PERSON>',
            '1. <PERSON><PERSON><PERSON><PERSON><PERSON>', '2. <PERSON><PERSON><PERSON><PERSON><PERSON>'
        ],
        'Ders Kodu': [
            'MAT101', 'FIZ201', 'BIL301', 'PRJ401',
            'KIM101', 'MAT201', 'ELE301', 'END401',
            'TUR101', 'ING201'
        ],
        '<PERSON><PERSON><PERSON><PERSON>': [
            'Prof. Dr. <PERSON><PERSON>', '<PERSON><PERSON>. Dr. <PERSON><PERSON>', 'Dr. <PERSON><PERSON><PERSON>', 'Prof. Dr. <PERSON><PERSON>',
            '<PERSON><PERSON>. Dr. <PERSON>', '<PERSON><PERSON> <PERSON><PERSON>', 'Prof. <PERSON><PERSON>', '<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>', 'Prof. Dr<PERSON>'
        ],
        '<PERSON><PERSON><PERSON><PERSON>ı<PERSON>ı': [
            120, 85, 45, 30, 
            110, 75, 55, 25,
            130, 90
        ],
        '<PERSON><PERSON>nav <PERSON>üresi': [
            90, 120, 90, 180,
            90, 120, 90, 180,
            60, 90
        ],
        '<PERSON>rc<PERSON> 1': [
            '2024-01-15', '2024-01-16', '2024-01-17', '2024-01-18',
            '2024-01-19', '2024-01-22', '2024-01-23', '2024-01-24',
            '2024-01-25', '2024-01-26'
        ],
        'Tercih 2': [
            '2024-01-16', '2024-01-17', '2024-01-18', '2024-01-19',
            '2024-01-22', '2024-01-23', '2024-01-24', '2024-01-25',
            '2024-01-26', '2024-01-29'
        ],
        'Tercih 3': [
            '2024-01-17', '2024-01-18', '2024-01-19', '2024-01-22',
            '2024-01-23', '2024-01-24', '2024-01-25', '2024-01-26',
            '2024-01-29', '2024-01-30'
        ],
        'Bilgisayar Gerekli': [
            'Hayır', 'Hayır', 'Evet', 'Evet',
            'Hayır', 'Hayır', 'Evet', 'Evet',
            'Hayır', 'Hayır'
        ],
        'Bölüm Derslikleri': [
            'A101, A102, A103', 'B201, B202, B203', 'C301, C302, LAB1', 'D401, D402, LAB2',
            'A101, A102, A103', 'B201, B202, B203', 'C301, C302, LAB1', 'D401, D402, LAB2',
            'A101, A102, A103', 'B201, B202, B203'
        ]
    }
    
    # DataFrame oluştur
    df = pd.DataFrame(data)
    
    # Excel dosyasına kaydet
    with pd.ExcelWriter('sinav_bilgileri_sablon.xlsx', engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Sınav Bilgileri', index=False)
        
        # Worksheet'i al ve sütun genişliklerini ayarla
        worksheet = writer.sheets['Sınav Bilgileri']
        
        # Sütun genişliklerini ayarla
        column_widths = {
            'A': 15,  # Sınıf Seviyesi
            'B': 12,  # Ders Kodu
            'C': 25,  # Öğretim Üyesi
            'D': 15,  # Öğrenci Sayısı
            'E': 12,  # Sınav Süresi
            'F': 12,  # Tercih 1
            'G': 12,  # Tercih 2
            'H': 12,  # Tercih 3
            'I': 15,  # Bilgisayar Gerekli
            'J': 30   # Bölüm Derslikleri
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
    
    print("✅ Excel şablonu 'sinav_bilgileri_sablon.xlsx' başarıyla oluşturuldu!")
    print("\n📋 Şablon içeriği:")
    print(df.to_string(index=False))

if __name__ == "__main__":
    create_excel_template()
