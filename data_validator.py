import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import re

class DataValidator:
    """Veri doğrulama ve kalite kontrol sınıfı"""
    
    def __init__(self, df):
        self.df = df.copy()
        self.validation_results = {
            'errors': [],
            'warnings': [],
            'info': [],
            'is_valid': True
        }
    
    def validate_all(self):
        """Tüm doğrulama kontrollerini yapar"""
        print("🔍 Veri doğrulama başlıyor...")
        
        self._validate_required_columns()
        self._validate_data_types()
        self._validate_business_rules()
        self._validate_data_consistency()
        self._validate_date_logic()
        
        # Sonuçları özetle
        self._summarize_results()
        
        return self.validation_results
    
    def _validate_required_columns(self):
        """Gerekli sütunların varlığını kontrol eder"""
        required_columns = [
            '<PERSON><PERSON>n<PERSON><PERSON>', '<PERSON><PERSON> Kodu', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ğ<PERSON><PERSON>',
            'Sınav Süresi', 'Tercih 1', 'Tercih 2', 'Tercih 3',
            'Bilgisayar Gerekli', 'Bölüm Derslikleri'
        ]
        
        missing_columns = set(required_columns) - set(self.df.columns)
        if missing_columns:
            self.validation_results['errors'].append(
                f"Eksik sütunlar: {', '.join(missing_columns)}"
            )
            self.validation_results['is_valid'] = False
        else:
            self.validation_results['info'].append("Tüm gerekli sütunlar mevcut")
    
    def _validate_data_types(self):
        """Veri tiplerini kontrol eder"""
        # Sayısal sütunlar
        numeric_columns = ['Öğrenci Sayısı', 'Sınav Süresi']
        for col in numeric_columns:
            if col in self.df.columns:
                if not pd.api.types.is_numeric_dtype(self.df[col]):
                    self.validation_results['errors'].append(
                        f"'{col}' sütunu sayısal olmalı"
                    )
                    self.validation_results['is_valid'] = False
        
        # Tarih sütunları
        date_columns = ['Tercih 1', 'Tercih 2', 'Tercih 3']
        for col in date_columns:
            if col in self.df.columns:
                if not pd.api.types.is_datetime64_any_dtype(self.df[col]):
                    self.validation_results['errors'].append(
                        f"'{col}' sütunu tarih formatında olmalı"
                    )
                    self.validation_results['is_valid'] = False
        
        # Boolean sütun
        if 'Bilgisayar Gerekli' in self.df.columns:
            if not pd.api.types.is_bool_dtype(self.df['Bilgisayar Gerekli']):
                self.validation_results['warnings'].append(
                    "'Bilgisayar Gerekli' sütunu boolean olmalı"
                )
    
    def _validate_business_rules(self):
        """İş kurallarını kontrol eder"""
        
        # Öğrenci sayısı kontrolleri
        if 'Öğrenci Sayısı' in self.df.columns:
            invalid_student_count = (self.df['Öğrenci Sayısı'] <= 0) | (self.df['Öğrenci Sayısı'] > 1000)
            if invalid_student_count.any():
                count = invalid_student_count.sum()
                self.validation_results['warnings'].append(
                    f"{count} satırda şüpheli öğrenci sayısı (1-1000 dışı)"
                )
        
        # Sınav süresi kontrolleri
        if 'Sınav Süresi' in self.df.columns:
            invalid_duration = (self.df['Sınav Süresi'] < 30) | (self.df['Sınav Süresi'] > 300)
            if invalid_duration.any():
                count = invalid_duration.sum()
                self.validation_results['warnings'].append(
                    f"{count} satırda şüpheli sınav süresi (30-300 dk dışı)"
                )
        
        # Ders kodu format kontrolleri
        if 'Ders Kodu' in self.df.columns:
            pattern = r'^[A-Z]{2,4}\d{3}$'
            invalid_codes = ~self.df['Ders Kodu'].str.match(pattern, na=False)
            if invalid_codes.any():
                count = invalid_codes.sum()
                self.validation_results['warnings'].append(
                    f"{count} satırda geçersiz ders kodu formatı (örn: MAT101)"
                )
        
        # Sınıf seviyesi kontrolleri
        if 'Sınıf Seviyesi' in self.df.columns:
            valid_levels = ['1. Sınıf', '2. Sınıf', '3. Sınıf', '4. Sınıf', 'Yüksek Lisans', 'Doktora']
            invalid_levels = ~self.df['Sınıf Seviyesi'].isin(valid_levels)
            if invalid_levels.any():
                count = invalid_levels.sum()
                self.validation_results['warnings'].append(
                    f"{count} satırda geçersiz sınıf seviyesi"
                )
    
    def _validate_data_consistency(self):
        """Veri tutarlılığını kontrol eder"""
        
        # Aynı ders kodunun tekrarı
        if 'Ders Kodu' in self.df.columns:
            duplicate_courses = self.df['Ders Kodu'].duplicated()
            if duplicate_courses.any():
                count = duplicate_courses.sum()
                self.validation_results['warnings'].append(
                    f"{count} satırda tekrarlanan ders kodu"
                )
        
        # Aynı öğretim üyesinin aynı tarihte birden fazla sınavı
        date_columns = ['Tercih 1', 'Tercih 2', 'Tercih 3']
        if 'Öğretim Üyesi' in self.df.columns and all(col in self.df.columns for col in date_columns):
            for date_col in date_columns:
                conflicts = self.df.groupby(['Öğretim Üyesi', date_col]).size()
                conflicts = conflicts[conflicts > 1]
                if len(conflicts) > 0:
                    self.validation_results['warnings'].append(
                        f"'{date_col}' tarihinde aynı öğretim üyesinin birden fazla sınavı var"
                    )
        
        # Bilgisayar gerekli olan sınavlar için uygun derslik kontrolü
        if 'Bilgisayar Gerekli' in self.df.columns and 'Bölüm Derslikleri' in self.df.columns:
            computer_required = self.df[self.df['Bilgisayar Gerekli'] == True]
            for idx, row in computer_required.iterrows():
                classrooms = str(row['Bölüm Derslikleri']).upper()
                if 'LAB' not in classrooms and 'BIL' not in classrooms:
                    self.validation_results['warnings'].append(
                        f"Satır {idx+1}: Bilgisayar gerekli ama uygun derslik yok"
                    )
    
    def _validate_date_logic(self):
        """Tarih mantığını kontrol eder"""
        date_columns = ['Tercih 1', 'Tercih 2', 'Tercih 3']
        
        if all(col in self.df.columns for col in date_columns):
            for idx, row in self.df.iterrows():
                dates = [row[col] for col in date_columns if pd.notna(row[col])]
                
                # Tarihlerin sıralı olup olmadığını kontrol et
                if len(dates) > 1:
                    for i in range(len(dates)-1):
                        if dates[i] >= dates[i+1]:
                            self.validation_results['warnings'].append(
                                f"Satır {idx+1}: Tercih tarihleri sıralı değil"
                            )
                            break
                
                # Geçmiş tarihleri kontrol et
                today = pd.Timestamp.now().date()
                for i, date_val in enumerate(dates):
                    if date_val.date() < today:
                        self.validation_results['warnings'].append(
                            f"Satır {idx+1}: {date_columns[i]} geçmiş tarih"
                        )
                
                # Aynı tarihleri kontrol et
                unique_dates = set(d.date() for d in dates)
                if len(unique_dates) < len(dates):
                    self.validation_results['warnings'].append(
                        f"Satır {idx+1}: Aynı tercih tarihleri var"
                    )
    
    def _summarize_results(self):
        """Doğrulama sonuçlarını özetler"""
        results = self.validation_results
        
        print(f"\n📊 DOĞRULAMA SONUÇLARI:")
        print(f"  • Toplam satır: {len(self.df)}")
        print(f"  • Hata sayısı: {len(results['errors'])}")
        print(f"  • Uyarı sayısı: {len(results['warnings'])}")
        print(f"  • Bilgi sayısı: {len(results['info'])}")
        print(f"  • Genel durum: {'✅ Geçerli' if results['is_valid'] else '❌ Geçersiz'}")
        
        if results['errors']:
            print(f"\n❌ HATALAR:")
            for error in results['errors']:
                print(f"  • {error}")
        
        if results['warnings']:
            print(f"\n⚠️ UYARILAR:")
            for warning in results['warnings']:
                print(f"  • {warning}")
        
        if results['info']:
            print(f"\n💡 BİLGİLER:")
            for info in results['info']:
                print(f"  • {info}")
    
    def get_data_quality_score(self):
        """Veri kalite skoru hesaplar (0-100)"""
        total_checks = len(self.validation_results['errors']) + len(self.validation_results['warnings'])
        if total_checks == 0:
            return 100
        
        error_weight = 10  # Her hata 10 puan düşürür
        warning_weight = 2  # Her uyarı 2 puan düşürür
        
        penalty = (len(self.validation_results['errors']) * error_weight + 
                  len(self.validation_results['warnings']) * warning_weight)
        
        score = max(0, 100 - penalty)
        return score
    
    def export_validation_report(self, file_path):
        """Doğrulama raporunu dosyaya yazar"""
        report = []
        report.append("VERİ DOĞRULAMA RAPORU")
        report.append("=" * 50)
        report.append(f"Tarih: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Toplam satır: {len(self.df)}")
        report.append(f"Kalite skoru: {self.get_data_quality_score()}/100")
        report.append("")
        
        if self.validation_results['errors']:
            report.append("HATALAR:")
            for error in self.validation_results['errors']:
                report.append(f"  • {error}")
            report.append("")
        
        if self.validation_results['warnings']:
            report.append("UYARILAR:")
            for warning in self.validation_results['warnings']:
                report.append(f"  • {warning}")
            report.append("")
        
        if self.validation_results['info']:
            report.append("BİLGİLER:")
            for info in self.validation_results['info']:
                report.append(f"  • {info}")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"✅ Doğrulama raporu kaydedildi: {file_path}")

def validate_excel_data(df):
    """Excel verisini doğrular"""
    validator = DataValidator(df)
    results = validator.validate_all()
    
    # Kalite skorunu göster
    score = validator.get_data_quality_score()
    print(f"\n🎯 Veri Kalite Skoru: {score}/100")
    
    # Raporu kaydet
    validator.export_validation_report("veri_dogrulama_raporu.txt")
    
    return results, score

if __name__ == "__main__":
    # Test için temizlenmiş veriyi doğrula
    try:
        df = pd.read_excel("sinav_bilgileri_sablon_temizlenmis.xlsx")
        results, score = validate_excel_data(df)
    except FileNotFoundError:
        print("❌ Temizlenmiş Excel dosyası bulunamadı. Önce excel_reader.py çalıştırın.")
