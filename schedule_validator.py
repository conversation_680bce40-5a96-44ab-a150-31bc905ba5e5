import pandas as pd
from exam_constraints import ExamConstraints
from classroom_allocator import ClassroomAllocator

def validate_final_schedule():
    """Final sınav programını doğrular"""
    
    print("🔍 FINAL SINAV PROGRAMI DOĞRULAMA")
    print("=" * 50)
    
    try:
        # Programı oku
        schedule_df = pd.read_excel("sinav_programi_final.xlsx")
        
        # Derslik bilgilerini oku
        classrooms_data = {
            'ad': ['A101', 'A102', 'A103', 'B201', 'B202', 'LAB1', 'LAB2', 'C301'],
            'kapasite': [150, 120, 100, 80, 60, 40, 35, 200],
            'bilgisayar_var_mi': [False, <PERSON>als<PERSON>, <PERSON>als<PERSON>, <PERSON>als<PERSON>, <PERSON>alse, True, True, True],
            'bolum': ['Mühendislik', 'Mühendislik', 'Mü<PERSON>islik', 'Mühendislik', 
                     'Mühendislik', '<PERSON><PERSON><PERSON><PERSON><PERSON>.', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>l'],
            'aktif': [True, True, True, True, True, True, True, True]
        }
        classrooms_df = pd.DataFrame(classrooms_data)
        
        # Kısıt kontrolcüsünü oluştur
        constraints = ExamConstraints()
        
        print(f"📊 Program özeti:")
        print(f"  • Toplam sınav: {len(schedule_df)}")
        print(f"  • Tarih aralığı: {schedule_df['Tarih'].min()} - {schedule_df['Tarih'].max()}")
        print(f"  • Kullanılan saatler: {sorted(schedule_df['Saat'].unique())}")
        print(f"  • Kullanılan derslikler: {sorted(schedule_df['Derslik'].unique())}")
        
        # Tüm kısıtları kontrol et
        validation_results = constraints.validate_all_constraints(
            schedule_df, 
            classrooms_df,
            exam_start_date="2024-01-15",
            exam_end_date="2024-01-30"
        )
        
        print(f"\n🎯 KISIT DOĞRULAMA SONUÇLARI:")
        print(f"  • Toplam ihlal: {validation_results['total_violations']}")
        print(f"  • Program geçerli: {'✅ Evet' if validation_results['is_valid'] else '❌ Hayır'}")
        
        # Kısıt bazında sonuçlar
        print(f"\n📋 Kısıt bazında sonuçlar:")
        for constraint, is_valid in validation_results['constraint_results'].items():
            status = "✅" if is_valid else "❌"
            print(f"  {status} {constraint}")
        
        # İhlaller varsa listele
        if validation_results['violations']:
            print(f"\n❌ TESPİT EDİLEN İHLALLER:")
            for violation in validation_results['violations']:
                print(f"  • {violation}")
        else:
            print(f"\n✅ HİÇ İHLAL TESPİT EDİLMEDİ!")
        
        # Ek analizler
        print_additional_analysis(schedule_df)
        
        return validation_results['is_valid']
        
    except Exception as e:
        print(f"❌ Doğrulama hatası: {e}")
        return False

def print_additional_analysis(schedule_df):
    """Ek analizleri yazdırır"""
    
    print(f"\n📈 EK ANALİZLER:")
    
    # Günlük sınav dağılımı
    daily_counts = schedule_df.groupby('Tarih').size()
    print(f"\n📅 Günlük sınav dağılımı:")
    for date, count in daily_counts.items():
        weekday = pd.to_datetime(date).strftime('%A')
        print(f"  • {date} ({weekday}): {count} sınav")
    
    # Saatlik dağılım
    hourly_counts = schedule_df.groupby('Saat').size()
    print(f"\n⏰ Saatlik dağılım:")
    for time, count in hourly_counts.items():
        print(f"  • {time}: {count} sınav")
    
    # Zorluk seviyesi dağılımı
    difficulty_counts = schedule_df.groupby('Zorluk Seviyesi').size()
    print(f"\n🎯 Zorluk seviyesi dağılımı:")
    for difficulty, count in difficulty_counts.items():
        print(f"  • {difficulty}: {count} sınav")
    
    # Derslik kullanımı
    classroom_counts = schedule_df.groupby('Derslik').size()
    print(f"\n🏫 Derslik kullanımı:")
    for classroom, count in classroom_counts.items():
        print(f"  • {classroom}: {count} sınav")
    
    # Bilgisayar gereksinimi
    computer_counts = schedule_df.groupby('Bilgisayar Gerekli').size()
    print(f"\n💻 Bilgisayar gereksinimi:")
    for need_computer, count in computer_counts.items():
        status = "Gerekli" if need_computer else "Gerekli değil"
        print(f"  • {status}: {count} sınav")
    
    # Tercih analizi
    analyze_preferences(schedule_df)

def analyze_preferences(schedule_df):
    """Tercih analizi yapar"""
    
    print(f"\n🎯 TERCİH ANALİZİ:")
    
    try:
        # Orijinal tercihleri oku
        original_df = pd.read_excel("sinav_bilgileri_sablon_temizlenmis_zorluk_eklenmis.xlsx")
        
        preference_matches = 0
        total_exams = len(schedule_df)
        
        for _, scheduled_exam in schedule_df.iterrows():
            course_code = scheduled_exam['Ders Kodu']
            scheduled_date = pd.to_datetime(scheduled_exam['Tarih']).date()
            
            # Orijinal tercihleri bul
            original_exam = original_df[original_df['Ders Kodu'] == course_code]
            
            if not original_exam.empty:
                original_exam = original_exam.iloc[0]
                
                # Tercihleri kontrol et
                preferences = []
                for pref_col in ['Tercih 1', 'Tercih 2', 'Tercih 3']:
                    if pref_col in original_exam.index and pd.notna(original_exam[pref_col]):
                        pref_date = pd.to_datetime(original_exam[pref_col]).date()
                        preferences.append(pref_date)
                
                # Eşleşme kontrolü
                if scheduled_date in preferences:
                    preference_matches += 1
                    match_rank = preferences.index(scheduled_date) + 1
                    print(f"  ✅ {course_code}: {match_rank}. tercih eşleşti")
                else:
                    print(f"  ❌ {course_code}: Tercih eşleşmedi")
        
        match_percentage = (preference_matches / total_exams) * 100 if total_exams > 0 else 0
        print(f"\n📊 Tercih eşleşme oranı: %{match_percentage:.1f} ({preference_matches}/{total_exams})")
        
    except Exception as e:
        print(f"  ⚠️ Tercih analizi yapılamadı: {e}")

def generate_final_report():
    """Final rapor oluşturur"""
    
    try:
        schedule_df = pd.read_excel("sinav_programi_final.xlsx")
        
        report = []
        report.append("SINAV PROGRAMI FINAL RAPORU")
        report.append("=" * 60)
        report.append(f"Oluşturulma Tarihi: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        report.append("PROGRAM ÖZETİ:")
        report.append(f"• Toplam sınav sayısı: {len(schedule_df)}")
        report.append(f"• Tarih aralığı: {schedule_df['Tarih'].min()} - {schedule_df['Tarih'].max()}")
        report.append(f"• Kullanılan gün sayısı: {schedule_df['Tarih'].nunique()}")
        report.append(f"• Kullanılan saat dilimi: {schedule_df['Saat'].nunique()}")
        report.append(f"• Kullanılan derslik sayısı: {schedule_df['Derslik'].nunique()}")
        report.append("")
        
        # Günlük program
        report.append("GÜNLÜK PROGRAM:")
        for date in sorted(schedule_df['Tarih'].unique()):
            daily_exams = schedule_df[schedule_df['Tarih'] == date].sort_values('Saat')
            weekday = pd.to_datetime(date).strftime('%A')
            report.append(f"\n{date} ({weekday}):")
            
            for _, exam in daily_exams.iterrows():
                report.append(f"  {exam['Saat']} - {exam['Ders Kodu']} ({exam['Sınıf Seviyesi']}) "
                             f"- {exam['Derslik']} - {exam['Öğrenci Sayısı']} öğrenci")
        
        report.append("")
        report.append("İSTATİSTİKLER:")
        
        # Zorluk dağılımı
        difficulty_counts = schedule_df['Zorluk Seviyesi'].value_counts()
        report.append("Zorluk Seviyesi Dağılımı:")
        for difficulty, count in difficulty_counts.items():
            report.append(f"  • {difficulty}: {count} sınav")
        
        # Derslik kullanımı
        classroom_counts = schedule_df['Derslik'].value_counts()
        report.append("\nDerslik Kullanımı:")
        for classroom, count in classroom_counts.items():
            report.append(f"  • {classroom}: {count} sınav")
        
        with open("sinav_programi_final_rapor.txt", "w", encoding="utf-8") as f:
            f.write("\n".join(report))
        
        print("✅ Final rapor kaydedildi: sinav_programi_final_rapor.txt")
        
    except Exception as e:
        print(f"❌ Rapor oluşturma hatası: {e}")

if __name__ == "__main__":
    # Final programı doğrula
    is_valid = validate_final_schedule()
    
    # Final rapor oluştur
    generate_final_report()
    
    if is_valid:
        print(f"\n🎉 SINAV PROGRAMI BAŞARIYLA OLUŞTURULDU!")
        print(f"📁 Dosyalar:")
        print(f"  • sinav_programi_final.xlsx - Ana program")
        print(f"  • sinav_programi_final_rapor.txt - Detaylı rapor")
    else:
        print(f"\n⚠️ Program oluşturuldu ancak bazı kısıt ihlalleri var!")
