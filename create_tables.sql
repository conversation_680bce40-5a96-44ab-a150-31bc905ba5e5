-- Sınav Programı Veritabanı Tabloları
-- MySQL için SQL script

-- Veritabanını oluştur
CREATE DATABASE IF NOT EXISTS sinav_programi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE sinav_programi;

-- 1. DE<PERSON>LE<PERSON> TABLOSU
CREATE TABLE IF NOT EXISTS dersler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kod VARCHAR(20) NOT NULL UNIQUE,
    ad VARCHAR(100) NOT NULL,
    akts INT DEFAULT 0,
    bolum VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. SINAVLAR TABLOSU
CREATE TABLE IF NOT EXISTS sinavlar (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ders_kodu VARCHAR(20) NOT NULL,
    ogretim_uyesi VARCHAR(100) NOT NULL,
    ogrenci_sayisi INT NOT NULL,
    sure INT NOT NULL COMMENT 'Dakika cinsinden',
    bilgisayar_gerekli BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ders_kodu) REFERENCES dersler(kod) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. SINAV TERCİHLERİ TABLOSU
CREATE TABLE IF NOT EXISTS sinav_tercihleri (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sinav_id INT NOT NULL,
    tercih1 DATE,
    tercih2 DATE,
    tercih3 DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sinav_id) REFERENCES sinavlar(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. DERSLİKLER TABLOSU
CREATE TABLE IF NOT EXISTS derslikler (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ad VARCHAR(50) NOT NULL UNIQUE,
    kapasite INT NOT NULL,
    bolum VARCHAR(50) NOT NULL,
    bilgisayar_var_mi BOOLEAN DEFAULT FALSE,
    aktif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. AYARLAR TABLOSU
CREATE TABLE IF NOT EXISTS ayarlar (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sinav_baslangic_tarihi DATE NOT NULL,
    sinav_bitis_tarihi DATE NOT NULL,
    gunluk_sinav_saatleri JSON COMMENT 'Günlük sınav saatleri',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ÖRNEK VERİLER

-- Örnek dersler
INSERT IGNORE INTO dersler (kod, ad, akts, bolum) VALUES
('MAT101', 'Matematik I', 6, 'Mühendislik'),
('FIZ201', 'Fizik II', 5, 'Mühendislik'),
('BIL301', 'Bilgisayar Programlama', 4, 'Bilgisayar Müh.'),
('PRJ401', 'Bitirme Projesi', 8, 'Mühendislik'),
('KIM101', 'Genel Kimya', 5, 'Mühendislik'),
('MAT201', 'Matematik II', 6, 'Mühendislik'),
('ELE301', 'Elektronik', 5, 'Elektrik Müh.'),
('END401', 'Endüstri Mühendisliği', 6, 'Endüstri Müh.'),
('TUR101', 'Türk Dili', 2, 'Genel'),
('ING201', 'İngilizce II', 3, 'Genel');

-- Örnek derslikler
INSERT IGNORE INTO derslikler (ad, kapasite, bolum, bilgisayar_var_mi) VALUES
('A101', 150, 'Mühendislik', FALSE),
('A102', 120, 'Mühendislik', FALSE),
('A103', 100, 'Mühendislik', FALSE),
('B201', 80, 'Mühendislik', FALSE),
('B202', 60, 'Mühendislik', FALSE),
('B203', 70, 'Mühendislik', FALSE),
('C301', 40, 'Bilgisayar Müh.', TRUE),
('C302', 35, 'Bilgisayar Müh.', TRUE),
('LAB1', 30, 'Bilgisayar Müh.', TRUE),
('LAB2', 25, 'Bilgisayar Müh.', TRUE),
('D401', 50, 'Endüstri Müh.', FALSE),
('D402', 45, 'Endüstri Müh.', FALSE);

-- Örnek ayarlar
INSERT IGNORE INTO ayarlar (sinav_baslangic_tarihi, sinav_bitis_tarihi, gunluk_sinav_saatleri) VALUES
('2024-01-15', '2024-01-30', '["09:00", "11:00", "14:00", "16:00"]');

-- İndeksler (performans için)
CREATE INDEX idx_sinavlar_ders_kodu ON sinavlar(ders_kodu);
CREATE INDEX idx_sinav_tercihleri_sinav_id ON sinav_tercihleri(sinav_id);
CREATE INDEX idx_derslikler_bolum ON derslikler(bolum);
CREATE INDEX idx_derslikler_bilgisayar ON derslikler(bilgisayar_var_mi);

-- Görünümler (Views)
CREATE OR REPLACE VIEW sinav_detaylari AS
SELECT 
    s.id,
    s.ders_kodu,
    d.ad as ders_adi,
    s.ogretim_uyesi,
    s.ogrenci_sayisi,
    s.sure,
    s.bilgisayar_gerekli,
    st.tercih1,
    st.tercih2,
    st.tercih3,
    d.bolum
FROM sinavlar s
LEFT JOIN dersler d ON s.ders_kodu = d.kod
LEFT JOIN sinav_tercihleri st ON s.id = st.sinav_id;
