import pandas as pd
from typing import List, Dict, Tuple, Optional
import numpy as np

class ClassroomAllocator:
    """Derslik tahsis ve kapasite yönetimi sınıfı"""
    
    def __init__(self, classrooms_df: pd.DataFrame):
        self.classrooms_df = classrooms_df.copy()
        self.allocation_results = []
        
    def get_suitable_classrooms(self, department: str, student_count: int, 
                              computer_required: bool = False) -> List[Dict]:
        """Uygun derslikleri bulur"""
        
        # Bölüm filtresi
        dept_classrooms = self.classrooms_df[
            (self.classrooms_df['bolum'] == department) | 
            (self.classrooms_df['bolum'] == 'Genel')
        ].copy()
        
        # Bilgisayar filtresi
        if computer_required:
            dept_classrooms = dept_classrooms[dept_classrooms['bilgisayar_var_mi'] == True]
        
        # Aktif derslikler
        dept_classrooms = dept_classrooms[dept_classrooms.get('aktif', True) == True]
        
        # Kapasiteye göre sırala (büyükten küçüğe)
        dept_classrooms = dept_classrooms.sort_values('kapasite', ascending=False)
        
        suitable_classrooms = []
        for _, classroom in dept_classrooms.iterrows():
            suitable_classrooms.append({
                'ad': classroom['ad'],
                'kapasite': classroom['kapasite'],
                'bilgisayar_var_mi': classroom['bilgisayar_var_mi'],
                'bolum': classroom['bolum']
            })
        
        return suitable_classrooms
    
    def allocate_classrooms_for_exam(self, exam_info: Dict) -> Dict:
        """Tek bir sınav için derslik tahsisi yapar"""
        
        student_count = exam_info['ogrenci_sayisi']
        department = exam_info.get('bolum', 'Genel')
        computer_required = exam_info.get('bilgisayar_gerekli', False)
        course_code = exam_info.get('ders_kodu', 'UNKNOWN')
        
        # Uygun derslikleri bul
        suitable_classrooms = self.get_suitable_classrooms(
            department, student_count, computer_required
        )
        
        if not suitable_classrooms:
            return {
                'success': False,
                'message': f'Uygun derslik bulunamadı (Bölüm: {department}, Bilgisayar: {computer_required})',
                'allocated_classrooms': [],
                'total_capacity': 0,
                'remaining_students': student_count
            }
        
        # Kapasite tahsisi
        allocated_classrooms = []
        remaining_students = student_count
        total_capacity = 0
        
        for classroom in suitable_classrooms:
            if remaining_students <= 0:
                break
                
            classroom_capacity = classroom['kapasite']
            students_in_this_room = min(remaining_students, classroom_capacity)
            
            allocated_classrooms.append({
                'ad': classroom['ad'],
                'kapasite': classroom_capacity,
                'atanan_ogrenci': students_in_this_room,
                'bilgisayar_var_mi': classroom['bilgisayar_var_mi']
            })
            
            remaining_students -= students_in_this_room
            total_capacity += students_in_this_room
        
        success = remaining_students <= 0
        
        result = {
            'success': success,
            'message': 'Başarılı' if success else f'{remaining_students} öğrenci için yer bulunamadı',
            'allocated_classrooms': allocated_classrooms,
            'total_capacity': total_capacity,
            'remaining_students': max(0, remaining_students),
            'classroom_list': ', '.join([c['ad'] for c in allocated_classrooms])
        }
        
        # Sonucu kaydet
        self.allocation_results.append({
            'ders_kodu': course_code,
            'ogrenci_sayisi': student_count,
            'tahsis_edilen_kapasite': total_capacity,
            'derslik_sayisi': len(allocated_classrooms),
            'derslik_listesi': result['classroom_list'],
            'basarili': success,
            'mesaj': result['message']
        })
        
        return result
    
    def allocate_multiple_exams(self, exams_list: List[Dict]) -> List[Dict]:
        """Birden fazla sınav için derslik tahsisi yapar"""
        
        results = []
        
        for exam in exams_list:
            result = self.allocate_classrooms_for_exam(exam)
            result['exam_info'] = exam
            results.append(result)
        
        return results
    
    def get_allocation_summary(self) -> Dict:
        """Tahsis özetini döndürür"""
        
        if not self.allocation_results:
            return {'message': 'Henüz tahsis yapılmamış'}
        
        total_exams = len(self.allocation_results)
        successful_allocations = sum(1 for r in self.allocation_results if r['basarili'])
        failed_allocations = total_exams - successful_allocations
        
        total_students = sum(r['ogrenci_sayisi'] for r in self.allocation_results)
        allocated_capacity = sum(r['tahsis_edilen_kapasite'] for r in self.allocation_results)
        
        # Derslik kullanım istatistikleri
        classroom_usage = {}
        for result in self.allocation_results:
            if result['basarili']:
                classrooms = result['derslik_listesi'].split(', ')
                for classroom in classrooms:
                    if classroom:
                        classroom_usage[classroom] = classroom_usage.get(classroom, 0) + 1
        
        return {
            'toplam_sinav': total_exams,
            'basarili_tahsis': successful_allocations,
            'basarisiz_tahsis': failed_allocations,
            'basari_orani': (successful_allocations / total_exams * 100) if total_exams > 0 else 0,
            'toplam_ogrenci': total_students,
            'tahsis_edilen_kapasite': allocated_capacity,
            'kapasite_kullanim_orani': (allocated_capacity / total_students * 100) if total_students > 0 else 0,
            'derslik_kullanim': classroom_usage,
            'en_cok_kullanilan_derslik': max(classroom_usage.items(), key=lambda x: x[1]) if classroom_usage else None
        }
    
    def print_allocation_report(self):
        """Tahsis raporunu yazdırır"""
        
        summary = self.get_allocation_summary()
        
        if 'message' in summary:
            print(summary['message'])
            return
        
        print("\n🏫 DERSLİK TAHSİS RAPORU")
        print("=" * 50)
        print(f"📊 Toplam sınav: {summary['toplam_sinav']}")
        print(f"✅ Başarılı tahsis: {summary['basarili_tahsis']}")
        print(f"❌ Başarısız tahsis: {summary['basarisiz_tahsis']}")
        print(f"📈 Başarı oranı: %{summary['basari_orani']:.1f}")
        print(f"👥 Toplam öğrenci: {summary['toplam_ogrenci']}")
        print(f"🏛️ Tahsis edilen kapasite: {summary['tahsis_edilen_kapasite']}")
        print(f"📊 Kapasite kullanım oranı: %{summary['kapasite_kullanim_orani']:.1f}")
        
        if summary['en_cok_kullanilan_derslik']:
            classroom, usage_count = summary['en_cok_kullanilan_derslik']
            print(f"🏆 En çok kullanılan derslik: {classroom} ({usage_count} sınav)")
        
        print(f"\n📋 Detaylı Tahsis Sonuçları:")
        for result in self.allocation_results:
            status = "✅" if result['basarili'] else "❌"
            print(f"  {status} {result['ders_kodu']}: {result['ogrenci_sayisi']} öğrenci → "
                  f"{result['derslik_listesi']} (Kapasite: {result['tahsis_edilen_kapasite']})")
            if not result['basarili']:
                print(f"      ⚠️ {result['mesaj']}")
    
    def export_allocation_results(self, filename: str):
        """Tahsis sonuçlarını Excel'e aktarır"""
        
        if not self.allocation_results:
            print("❌ Aktarılacak tahsis sonucu yok")
            return False
        
        df_results = pd.DataFrame(self.allocation_results)
        
        try:
            df_results.to_excel(filename, index=False)
            print(f"✅ Tahsis sonuçları kaydedildi: {filename}")
            return True
        except Exception as e:
            print(f"❌ Dosya kaydetme hatası: {e}")
            return False

def test_classroom_allocation():
    """Derslik tahsis testini yapar"""
    
    print("🧪 DERSLİK TAHSİS TESTİ")
    print("=" * 40)
    
    # Test derslikleri
    classrooms_data = {
        'ad': ['A101', 'A102', 'A103', 'B201', 'B202', 'LAB1', 'LAB2', 'C301'],
        'kapasite': [150, 120, 100, 80, 60, 40, 35, 200],
        'bilgisayar_var_mi': [False, False, False, False, False, True, True, True],
        'bolum': ['Mühendislik', 'Mühendislik', 'Mühendislik', 'Mühendislik', 
                 'Mühendislik', 'Bilgisayar Müh.', 'Bilgisayar Müh.', 'Genel'],
        'aktif': [True, True, True, True, True, True, True, True]
    }
    
    classrooms_df = pd.DataFrame(classrooms_data)
    allocator = ClassroomAllocator(classrooms_df)
    
    # Test sınavları
    test_exams = [
        {
            'ders_kodu': 'MAT101',
            'ogrenci_sayisi': 120,
            'bolum': 'Mühendislik',
            'bilgisayar_gerekli': False
        },
        {
            'ders_kodu': 'BIL301',
            'ogrenci_sayisi': 45,
            'bolum': 'Bilgisayar Müh.',
            'bilgisayar_gerekli': True
        },
        {
            'ders_kodu': 'FIZ201',
            'ogrenci_sayisi': 300,  # Çok büyük sınıf
            'bolum': 'Mühendislik',
            'bilgisayar_gerekli': False
        },
        {
            'ders_kodu': 'PRJ401',
            'ogrenci_sayisi': 30,
            'bolum': 'Bilgisayar Müh.',
            'bilgisayar_gerekli': True
        }
    ]
    
    # Tahsis yap
    results = allocator.allocate_multiple_exams(test_exams)
    
    # Sonuçları göster
    for i, result in enumerate(results):
        exam = test_exams[i]
        print(f"\n📋 Test {i+1}: {exam['ders_kodu']}")
        print(f"  Öğrenci sayısı: {exam['ogrenci_sayisi']}")
        print(f"  Bilgisayar gerekli: {exam['bilgisayar_gerekli']}")
        print(f"  Sonuç: {'✅ Başarılı' if result['success'] else '❌ Başarısız'}")
        print(f"  Mesaj: {result['message']}")
        if result['allocated_classrooms']:
            print(f"  Atanan derslikler:")
            for classroom in result['allocated_classrooms']:
                print(f"    • {classroom['ad']}: {classroom['atanan_ogrenci']}/{classroom['kapasite']} öğrenci")
    
    # Özet rapor
    allocator.print_allocation_report()
    
    return allocator

if __name__ == "__main__":
    allocator = test_classroom_allocation()
    
    # Sonuçları kaydet
    allocator.export_allocation_results("derslik_tahsis_sonuclari.xlsx")
