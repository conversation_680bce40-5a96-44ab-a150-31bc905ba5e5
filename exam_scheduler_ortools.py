import pandas as pd
from ortools.sat.python import cp_model
from datetime import datetime, timedelta, time
from typing import List, Dict, Tuple, Optional
import numpy as np
from exam_constraints import ExamConstraints
from classroom_allocator import ClassroomAllocator

class ExamSchedulerORTools:
    """OR-Tools ile sınav programı optimizasyonu"""
    
    def __init__(self, exams_df: pd.DataFrame, classrooms_df: pd.DataFrame, 
                 exam_start_date: str = "2024-01-15", exam_end_date: str = "2024-01-30"):
        
        self.exams_df = exams_df.copy()
        self.classrooms_df = classrooms_df.copy()
        self.exam_start_date = pd.to_datetime(exam_start_date)
        self.exam_end_date = pd.to_datetime(exam_end_date)
        
        # Kısıt ve tahsis sınıfları
        self.constraints = ExamConstraints()
        self.allocator = ClassroomAllocator(classrooms_df)
        
        # OR-Tools modeli
        self.model = cp_model.CpModel()
        self.solver = cp_model.CpSolver()
        
        # Değişkenler ve veriler
        self.exam_vars = {}
        self.dates = []
        self.time_slots = []
        self.exam_indices = {}
        self.solution_data = []
        
        # Sonuçlar
        self.scheduling_results = {
            'success': False,
            'scheduled_exams': [],
            'unscheduled_exams': [],
            'total_exams': len(exams_df),
            'optimization_status': None
        }
        
        self._prepare_data()
    
    def _prepare_data(self):
        """Veri hazırlama"""
        
        # Tarih aralığını oluştur (sadece hafta içi)
        current_date = self.exam_start_date
        while current_date <= self.exam_end_date:
            if current_date.weekday() < 5:  # Pazartesi-Cuma
                self.dates.append(current_date)
            current_date += timedelta(days=1)
        
        # Saat dilimlerini oluştur
        self.time_slots = self.constraints.exam_time_slots
        
        # Sınav indekslerini oluştur
        for i, (_, exam) in enumerate(self.exams_df.iterrows()):
            self.exam_indices[exam['Ders Kodu']] = i
        
        print(f"📅 Sınav tarihleri: {len(self.dates)} gün")
        print(f"⏰ Saat dilimleri: {len(self.time_slots)} slot")
        print(f"📚 Sınavlar: {len(self.exams_df)} adet")
    
    def _create_variables(self):
        """OR-Tools değişkenlerini oluşturur"""
        
        # Her sınav için tarih-saat-derslik kombinasyonu
        for exam_idx, (_, exam) in enumerate(self.exams_df.iterrows()):
            course_code = exam['Ders Kodu']
            
            for date_idx, date in enumerate(self.dates):
                # Geçerli saat dilimlerini al
                valid_slots = self.constraints.get_valid_time_slots(pd.Timestamp(date))
                
                for time_slot in valid_slots:
                    time_idx = self.time_slots.index(time_slot) if time_slot in self.time_slots else -1
                    if time_idx >= 0:
                        # Değişken adı: exam_courseCode_dateIdx_timeIdx
                        var_name = f"exam_{course_code}_{date_idx}_{time_idx}"
                        self.exam_vars[var_name] = self.model.NewBoolVar(var_name)
        
        print(f"🔢 Oluşturulan değişken sayısı: {len(self.exam_vars)}")
    
    def _add_basic_constraints(self):
        """Temel kısıtları ekler"""
        
        # Kısıt 1: Her sınav tam olarak bir kez atanmalı
        for exam_idx, (_, exam) in enumerate(self.exams_df.iterrows()):
            course_code = exam['Ders Kodu']
            
            exam_assignment_vars = []
            for var_name, var in self.exam_vars.items():
                if var_name.startswith(f"exam_{course_code}_"):
                    exam_assignment_vars.append(var)
            
            if exam_assignment_vars:
                self.model.Add(sum(exam_assignment_vars) == 1)
        
        print("✅ Temel kısıtlar eklendi")
    
    def _add_class_level_constraints(self):
        """Sınıf seviyesi çakışma kısıtlarını ekler"""
        
        # Aynı sınıf seviyesi dersleri aynı saatte olamaz
        for date_idx, date in enumerate(self.dates):
            for time_idx, time_slot in enumerate(self.time_slots):
                
                # Her sınıf seviyesi için
                class_levels = self.exams_df['Sınıf Seviyesi'].unique()
                
                for class_level in class_levels:
                    same_level_exams = self.exams_df[
                        self.exams_df['Sınıf Seviyesi'] == class_level
                    ]
                    
                    same_level_vars = []
                    for _, exam in same_level_exams.iterrows():
                        course_code = exam['Ders Kodu']
                        var_name = f"exam_{course_code}_{date_idx}_{time_idx}"
                        if var_name in self.exam_vars:
                            same_level_vars.append(self.exam_vars[var_name])
                    
                    if len(same_level_vars) > 1:
                        self.model.Add(sum(same_level_vars) <= 1)
        
        print("✅ Sınıf seviyesi kısıtları eklendi")
    
    def _add_difficulty_constraints(self):
        """Zorluk seviyesi kısıtlarını ekler"""
        
        # Aynı gün iki zor/çok zor sınav olamaz
        for date_idx, date in enumerate(self.dates):
            
            difficult_exams = self.exams_df[
                self.exams_df['Zorluk Seviyesi'].isin(['Zor', 'Çok Zor'])
            ]
            
            daily_difficult_vars = []
            for _, exam in difficult_exams.iterrows():
                course_code = exam['Ders Kodu']
                
                for time_idx in range(len(self.time_slots)):
                    var_name = f"exam_{course_code}_{date_idx}_{time_idx}"
                    if var_name in self.exam_vars:
                        daily_difficult_vars.append(self.exam_vars[var_name])
            
            if len(daily_difficult_vars) > 1:
                self.model.Add(sum(daily_difficult_vars) <= 1)
        
        print("✅ Zorluk seviyesi kısıtları eklendi")
    
    def _add_preference_objectives(self):
        """Tercih tabanlı amaç fonksiyonu ekler"""
        
        preference_score = []
        
        for exam_idx, (_, exam) in enumerate(self.exams_df.iterrows()):
            course_code = exam['Ders Kodu']
            
            # Tercih tarihlerini al
            preferences = []
            for pref_col in ['Tercih 1', 'Tercih 2', 'Tercih 3']:
                if pref_col in exam.index and pd.notna(exam[pref_col]):
                    pref_date = pd.to_datetime(exam[pref_col])
                    if self.exam_start_date <= pref_date <= self.exam_end_date:
                        preferences.append(pref_date)
            
            # Her atama için tercih puanı hesapla
            for date_idx, date in enumerate(self.dates):
                for time_idx, time_slot in enumerate(self.time_slots):
                    var_name = f"exam_{course_code}_{date_idx}_{time_idx}"
                    
                    if var_name in self.exam_vars:
                        # Tercih puanı hesapla
                        score = 0
                        current_date = pd.Timestamp(date)
                        
                        if preferences:
                            for i, pref_date in enumerate(preferences):
                                if current_date.date() == pref_date.date():
                                    score = 100 - (i * 20)  # 1. tercih: 100, 2. tercih: 80, 3. tercih: 60
                                    break
                            
                            if score == 0:
                                # Tercihlere yakınlık puanı
                                min_distance = min(abs((current_date - pref).days) for pref in preferences)
                                score = max(0, 40 - min_distance * 5)
                        
                        # Zorluk seviyesi bonusu (kolay sınavları erken saatlere)
                        if exam['Zorluk Seviyesi'] == 'Kolay' and time_idx < 2:
                            score += 10
                        elif exam['Zorluk Seviyesi'] == 'Çok Zor' and time_idx >= 2:
                            score += 10
                        
                        if score > 0:
                            preference_score.append(
                                self.exam_vars[var_name] * score
                            )
        
        # Amaç fonksiyonu: Toplam tercihi maksimize et
        if preference_score:
            self.model.Maximize(sum(preference_score))
        
        print("✅ Tercih tabanlı amaç fonksiyonu eklendi")
    
    def solve_schedule(self) -> bool:
        """Optimizasyon problemini çözer"""
        
        print("\n🔧 OR-Tools modeli oluşturuluyor...")
        
        # Değişkenleri oluştur
        self._create_variables()
        
        # Kısıtları ekle
        self._add_basic_constraints()
        self._add_class_level_constraints()
        self._add_difficulty_constraints()
        
        # Amaç fonksiyonunu ekle
        self._add_preference_objectives()
        
        print("\n🚀 Optimizasyon başlıyor...")
        
        # Çözücü ayarları
        self.solver.parameters.max_time_in_seconds = 60.0  # 1 dakika limit
        self.solver.parameters.num_search_workers = 4
        
        # Çöz
        status = self.solver.Solve(self.model)
        
        # Sonuçları işle
        self._process_results(status)
        
        return self.scheduling_results['success']
    
    def _process_results(self, status):
        """Çözüm sonuçlarını işler"""
        
        status_names = {
            cp_model.OPTIMAL: "OPTIMAL",
            cp_model.FEASIBLE: "FEASIBLE", 
            cp_model.INFEASIBLE: "INFEASIBLE",
            cp_model.UNKNOWN: "UNKNOWN",
            cp_model.MODEL_INVALID: "MODEL_INVALID"
        }
        
        self.scheduling_results['optimization_status'] = status_names.get(status, "UNKNOWN")
        
        if status in [cp_model.OPTIMAL, cp_model.FEASIBLE]:
            self.scheduling_results['success'] = True
            
            # Çözümü çıkar
            scheduled_exams = []
            
            for exam_idx, (_, exam) in enumerate(self.exams_df.iterrows()):
                course_code = exam['Ders Kodu']
                scheduled = False
                
                for date_idx, date in enumerate(self.dates):
                    for time_idx, time_slot in enumerate(self.time_slots):
                        var_name = f"exam_{course_code}_{date_idx}_{time_idx}"
                        
                        if var_name in self.exam_vars and self.solver.Value(self.exam_vars[var_name]) == 1:
                            
                            # Derslik tahsisi yap
                            allocation_result = self._allocate_classroom_for_exam(exam, date, time_slot)
                            
                            scheduled_exam = {
                                'Ders Kodu': course_code,
                                'Sınıf Seviyesi': exam['Sınıf Seviyesi'],
                                'Öğretim Üyesi': exam['Öğretim Üyesi'],
                                'Öğrenci Sayısı': exam['Öğrenci Sayısı'],
                                'Sınav Süresi': exam['Sınav Süresi'],
                                'Zorluk Seviyesi': exam['Zorluk Seviyesi'],
                                'Bilgisayar Gerekli': exam['Bilgisayar Gerekli'],
                                'Tarih': date.strftime('%Y-%m-%d'),
                                'Saat': time_slot.strftime('%H:%M:%S'),
                                'Derslik': allocation_result['classroom_list'],
                                'Tahsis Durumu': 'Başarılı' if allocation_result['success'] else 'Başarısız'
                            }
                            
                            scheduled_exams.append(scheduled_exam)
                            scheduled = True
                            break
                    
                    if scheduled:
                        break
                
                if not scheduled:
                    self.scheduling_results['unscheduled_exams'].append(course_code)
            
            self.scheduling_results['scheduled_exams'] = scheduled_exams
            
        else:
            self.scheduling_results['success'] = False
            self.scheduling_results['unscheduled_exams'] = self.exams_df['Ders Kodu'].tolist()
        
        print(f"\n📊 Optimizasyon sonucu: {self.scheduling_results['optimization_status']}")
        print(f"✅ Programlanan sınav: {len(self.scheduling_results['scheduled_exams'])}")
        print(f"❌ Programlanamayan sınav: {len(self.scheduling_results['unscheduled_exams'])}")
    
    def _allocate_classroom_for_exam(self, exam, date, time_slot) -> Dict:
        """Sınav için derslik tahsisi yapar"""
        
        exam_info = {
            'ders_kodu': exam['Ders Kodu'],
            'ogrenci_sayisi': exam['Öğrenci Sayısı'],
            'bolum': exam.get('bolum', 'Genel'),
            'bilgisayar_gerekli': exam['Bilgisayar Gerekli']
        }
        
        return self.allocator.allocate_classrooms_for_exam(exam_info)
    
    def get_schedule_dataframe(self) -> pd.DataFrame:
        """Programı DataFrame olarak döndürür"""
        
        if not self.scheduling_results['scheduled_exams']:
            return pd.DataFrame()
        
        return pd.DataFrame(self.scheduling_results['scheduled_exams'])
    
    def export_schedule(self, filename: str):
        """Programı Excel'e aktarır"""
        
        df_schedule = self.get_schedule_dataframe()
        
        if df_schedule.empty:
            print("❌ Aktarılacak program yok")
            return False
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df_schedule.to_excel(writer, sheet_name='Sınav Programı', index=False)
                
                # Özet sayfa
                summary_data = {
                    'Metrik': ['Toplam Sınav', 'Programlanan', 'Programlanamayan', 'Başarı Oranı'],
                    'Değer': [
                        self.scheduling_results['total_exams'],
                        len(self.scheduling_results['scheduled_exams']),
                        len(self.scheduling_results['unscheduled_exams']),
                        f"%{len(self.scheduling_results['scheduled_exams']) / self.scheduling_results['total_exams'] * 100:.1f}"
                    ]
                }
                
                pd.DataFrame(summary_data).to_excel(writer, sheet_name='Özet', index=False)
            
            print(f"✅ Sınav programı kaydedildi: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Dosya kaydetme hatası: {e}")
            return False
    
    def print_schedule_summary(self):
        """Program özetini yazdırır"""
        
        print(f"\n📋 SINAV PROGRAMI ÖZETİ")
        print("=" * 50)
        print(f"🎯 Optimizasyon durumu: {self.scheduling_results['optimization_status']}")
        print(f"📊 Toplam sınav: {self.scheduling_results['total_exams']}")
        print(f"✅ Programlanan: {len(self.scheduling_results['scheduled_exams'])}")
        print(f"❌ Programlanamayan: {len(self.scheduling_results['unscheduled_exams'])}")
        
        if self.scheduling_results['total_exams'] > 0:
            success_rate = len(self.scheduling_results['scheduled_exams']) / self.scheduling_results['total_exams'] * 100
            print(f"📈 Başarı oranı: %{success_rate:.1f}")
        
        if self.scheduling_results['unscheduled_exams']:
            print(f"\n❌ Programlanamayan sınavlar:")
            for course in self.scheduling_results['unscheduled_exams']:
                print(f"  • {course}")

def run_exam_scheduling():
    """Ana sınav programlama fonksiyonu"""
    
    print("🎯 SINAV PROGRAMLAMA SİSTEMİ")
    print("=" * 50)
    
    try:
        # Veri dosyalarını oku
        exams_df = pd.read_excel("sinav_bilgileri_sablon_temizlenmis_zorluk_eklenmis.xlsx")
        
        # Örnek derslik verisi (gerçek uygulamada veritabanından gelecek)
        classrooms_data = {
            'ad': ['A101', 'A102', 'A103', 'B201', 'B202', 'LAB1', 'LAB2', 'C301'],
            'kapasite': [150, 120, 100, 80, 60, 40, 35, 200],
            'bilgisayar_var_mi': [False, False, False, False, False, True, True, True],
            'bolum': ['Mühendislik', 'Mühendislik', 'Mühendislik', 'Mühendislik', 
                     'Mühendislik', 'Bilgisayar Müh.', 'Bilgisayar Müh.', 'Genel'],
            'aktif': [True, True, True, True, True, True, True, True]
        }
        classrooms_df = pd.DataFrame(classrooms_data)
        
        # Scheduler'ı oluştur
        scheduler = ExamSchedulerORTools(
            exams_df, 
            classrooms_df,
            exam_start_date="2024-01-15",
            exam_end_date="2024-01-30"
        )
        
        # Programlamayı çalıştır
        success = scheduler.solve_schedule()
        
        # Sonuçları göster
        scheduler.print_schedule_summary()
        
        # Dosyaya kaydet
        if success:
            scheduler.export_schedule("sinav_programi_final.xlsx")
        
        return scheduler
        
    except Exception as e:
        print(f"❌ Hata: {e}")
        return None

if __name__ == "__main__":
    scheduler = run_exam_scheduling()
