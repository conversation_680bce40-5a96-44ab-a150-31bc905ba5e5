import pandas as pd
import numpy as np
from datetime import datetime, date
import re
import warnings
warnings.filterwarnings('ignore')

class ExcelDataProcessor:
    """Excel dosyasını okur ve veri temizleme işlemlerini yapar"""
    
    def __init__(self, file_path):
        self.file_path = file_path
        self.df = None
        self.errors = []
        self.warnings = []
    
    def read_excel(self):
        """Excel dosyasını okur"""
        try:
            # Excel dosyasını oku
            self.df = pd.read_excel(self.file_path, sheet_name=0)
            print(f"✅ Excel dosyası başarıyla okundu: {len(self.df)} satır")
            
            # Sütun isimlerini kontrol et ve düzenle
            self._clean_column_names()
            
            return True
            
        except FileNotFoundError:
            self.errors.append(f"❌ Dosya bulunamadı: {self.file_path}")
            return False
        except Exception as e:
            self.errors.append(f"❌ Excel okuma hatası: {str(e)}")
            return False
    
    def _clean_column_names(self):
        """Sütun isimlerini temizler ve standartlaştırır"""
        # Beklenen sütun isimleri
        expected_columns = [
            'Sınıf Seviyesi', 'Ders Kodu', 'Öğretim Üyesi', 'Öğrenci Sayısı',
            'Sınav Süresi', 'Tercih 1', 'Tercih 2', 'Tercih 3',
            'Bilgisayar Gerekli', 'Bölüm Derslikleri'
        ]
        
        # Sütun isimlerini temizle (boşlukları kaldır)
        self.df.columns = self.df.columns.str.strip()
        
        # Eksik sütunları kontrol et
        missing_columns = set(expected_columns) - set(self.df.columns)
        if missing_columns:
            self.errors.append(f"❌ Eksik sütunlar: {', '.join(missing_columns)}")
        
        print(f"📋 Bulunan sütunlar: {list(self.df.columns)}")
    
    def clean_data(self):
        """Veri temizleme işlemlerini yapar"""
        if self.df is None:
            self.errors.append("❌ Önce Excel dosyasını okuyun")
            return False
        
        print("\n🧹 Veri temizleme başlıyor...")
        
        # 1. Boş satırları kaldır
        initial_rows = len(self.df)
        self.df = self.df.dropna(how='all')
        removed_rows = initial_rows - len(self.df)
        if removed_rows > 0:
            print(f"  • {removed_rows} boş satır kaldırıldı")
        
        # 2. Sınıf seviyesini temizle
        self._clean_class_level()
        
        # 3. Ders kodunu temizle
        self._clean_course_code()
        
        # 4. Öğretim üyesi ismini temizle
        self._clean_instructor_name()
        
        # 5. Sayısal değerleri temizle
        self._clean_numeric_values()
        
        # 6. Tarih değerlerini temizle
        self._clean_date_values()
        
        # 7. Boolean değerleri temizle
        self._clean_boolean_values()
        
        # 8. Derslik listesini temizle
        self._clean_classroom_list()
        
        # 9. Son kontroller
        self._final_validation()
        
        return len(self.errors) == 0
    
    def _clean_class_level(self):
        """Sınıf seviyesi sütununu temizler"""
        if 'Sınıf Seviyesi' in self.df.columns:
            # Boş değerleri kontrol et
            null_count = self.df['Sınıf Seviyesi'].isnull().sum()
            if null_count > 0:
                self.warnings.append(f"⚠️ {null_count} satırda sınıf seviyesi boş")
            
            # String'e çevir ve temizle
            self.df['Sınıf Seviyesi'] = self.df['Sınıf Seviyesi'].astype(str).str.strip()
            print("  ✅ Sınıf seviyesi temizlendi")
    
    def _clean_course_code(self):
        """Ders kodu sütununu temizler"""
        if 'Ders Kodu' in self.df.columns:
            # Boş değerleri kontrol et
            null_count = self.df['Ders Kodu'].isnull().sum()
            if null_count > 0:
                self.errors.append(f"❌ {null_count} satırda ders kodu boş")
            
            # Büyük harfe çevir ve temizle
            self.df['Ders Kodu'] = self.df['Ders Kodu'].astype(str).str.upper().str.strip()
            
            # Ders kodu formatını kontrol et (örn: MAT101)
            pattern = r'^[A-Z]{2,4}\d{3}$'
            invalid_codes = ~self.df['Ders Kodu'].str.match(pattern, na=False)
            if invalid_codes.any():
                invalid_count = invalid_codes.sum()
                self.warnings.append(f"⚠️ {invalid_count} satırda geçersiz ders kodu formatı")
            
            print("  ✅ Ders kodu temizlendi")
    
    def _clean_instructor_name(self):
        """Öğretim üyesi ismini temizler"""
        if 'Öğretim Üyesi' in self.df.columns:
            # Boş değerleri kontrol et
            null_count = self.df['Öğretim Üyesi'].isnull().sum()
            if null_count > 0:
                self.errors.append(f"❌ {null_count} satırda öğretim üyesi boş")
            
            # Temizle ve başlık formatına çevir
            self.df['Öğretim Üyesi'] = self.df['Öğretim Üyesi'].astype(str).str.strip().str.title()
            print("  ✅ Öğretim üyesi isimleri temizlendi")
    
    def _clean_numeric_values(self):
        """Sayısal değerleri temizler"""
        numeric_columns = ['Öğrenci Sayısı', 'Sınav Süresi']
        
        for col in numeric_columns:
            if col in self.df.columns:
                # Sayısal değere çevir
                self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
                
                # Negatif değerleri kontrol et
                negative_count = (self.df[col] < 0).sum()
                if negative_count > 0:
                    self.errors.append(f"❌ {col} sütununda {negative_count} negatif değer")
                
                # Boş değerleri kontrol et
                null_count = self.df[col].isnull().sum()
                if null_count > 0:
                    self.errors.append(f"❌ {col} sütununda {null_count} geçersiz değer")
                
                # Mantıklı aralıkları kontrol et
                if col == 'Öğrenci Sayısı':
                    invalid = (self.df[col] > 500) | (self.df[col] < 1)
                    if invalid.any():
                        self.warnings.append(f"⚠️ {col} sütununda şüpheli değerler (1-500 dışı)")
                
                elif col == 'Sınav Süresi':
                    invalid = (self.df[col] > 300) | (self.df[col] < 30)
                    if invalid.any():
                        self.warnings.append(f"⚠️ {col} sütununda şüpheli değerler (30-300 dk dışı)")
        
        print("  ✅ Sayısal değerler temizlendi")
    
    def _clean_date_values(self):
        """Tarih değerlerini temizler"""
        date_columns = ['Tercih 1', 'Tercih 2', 'Tercih 3']
        
        for col in date_columns:
            if col in self.df.columns:
                # Tarihleri pandas datetime'a çevir
                self.df[col] = pd.to_datetime(self.df[col], errors='coerce', dayfirst=True)
                
                # Geçersiz tarihleri kontrol et
                null_count = self.df[col].isnull().sum()
                if null_count > 0:
                    self.warnings.append(f"⚠️ {col} sütununda {null_count} geçersiz tarih")
                
                # Geçmiş tarihleri kontrol et
                today = pd.Timestamp.now()
                past_dates = (self.df[col] < today).sum()
                if past_dates > 0:
                    self.warnings.append(f"⚠️ {col} sütununda {past_dates} geçmiş tarih")
        
        print("  ✅ Tarih değerleri temizlendi")
    
    def _clean_boolean_values(self):
        """Boolean değerleri temizler"""
        if 'Bilgisayar Gerekli' in self.df.columns:
            # String'e çevir ve temizle
            self.df['Bilgisayar Gerekli'] = self.df['Bilgisayar Gerekli'].astype(str).str.strip().str.lower()
            
            # Boolean değerlere çevir
            boolean_map = {
                'evet': True, 'yes': True, 'true': True, '1': True,
                'hayır': False, 'no': False, 'false': False, '0': False
            }
            
            self.df['Bilgisayar Gerekli'] = self.df['Bilgisayar Gerekli'].map(boolean_map)
            
            # Geçersiz değerleri kontrol et
            null_count = self.df['Bilgisayar Gerekli'].isnull().sum()
            if null_count > 0:
                self.errors.append(f"❌ {null_count} satırda geçersiz 'Bilgisayar Gerekli' değeri")
            
            print("  ✅ Boolean değerler temizlendi")
    
    def _clean_classroom_list(self):
        """Derslik listesini temizler"""
        if 'Bölüm Derslikleri' in self.df.columns:
            # Boş değerleri kontrol et
            null_count = self.df['Bölüm Derslikleri'].isnull().sum()
            if null_count > 0:
                self.warnings.append(f"⚠️ {null_count} satırda derslik listesi boş")
            
            # Temizle ve standartlaştır
            self.df['Bölüm Derslikleri'] = self.df['Bölüm Derslikleri'].astype(str).str.strip()
            
            # Virgülle ayrılmış listeyi kontrol et
            def clean_classroom_list(classroom_str):
                if pd.isna(classroom_str) or classroom_str == 'nan':
                    return ''
                # Virgülle ayır, temizle ve tekrar birleştir
                classrooms = [c.strip().upper() for c in str(classroom_str).split(',')]
                return ', '.join([c for c in classrooms if c])
            
            self.df['Bölüm Derslikleri'] = self.df['Bölüm Derslikleri'].apply(clean_classroom_list)
            print("  ✅ Derslik listeleri temizlendi")
    
    def _final_validation(self):
        """Son kontrolleri yapar"""
        # Kritik sütunlarda boş değer kontrolü
        critical_columns = ['Ders Kodu', 'Öğretim Üyesi', 'Öğrenci Sayısı', 'Sınav Süresi']
        
        for col in critical_columns:
            if col in self.df.columns:
                null_count = self.df[col].isnull().sum()
                if null_count > 0:
                    self.errors.append(f"❌ Kritik sütun '{col}' da {null_count} boş değer")
        
        print("  ✅ Son kontroller tamamlandı")
    
    def get_summary(self):
        """Veri özeti döndürür"""
        if self.df is None:
            return "Veri yüklenmedi"
        
        summary = {
            'total_rows': len(self.df),
            'total_columns': len(self.df.columns),
            'errors': len(self.errors),
            'warnings': len(self.warnings),
            'data_types': self.df.dtypes.to_dict(),
            'null_counts': self.df.isnull().sum().to_dict()
        }
        
        return summary
    
    def save_cleaned_data(self, output_path):
        """Temizlenmiş veriyi kaydet"""
        if self.df is None:
            return False
        
        try:
            self.df.to_excel(output_path, index=False)
            print(f"✅ Temizlenmiş veri kaydedildi: {output_path}")
            return True
        except Exception as e:
            self.errors.append(f"❌ Kaydetme hatası: {str(e)}")
            return False

def process_excel_file(file_path):
    """Excel dosyasını işler ve temizler"""
    print(f"📖 Excel dosyası işleniyor: {file_path}")
    
    processor = ExcelDataProcessor(file_path)
    
    # Excel'i oku
    if not processor.read_excel():
        print("❌ Excel dosyası okunamadı!")
        for error in processor.errors:
            print(error)
        return None
    
    # Veriyi temizle
    if processor.clean_data():
        print("✅ Veri temizleme başarılı!")
    else:
        print("⚠️ Veri temizleme sırasında hatalar oluştu!")
    
    # Hataları ve uyarıları göster
    if processor.errors:
        print("\n❌ HATALAR:")
        for error in processor.errors:
            print(f"  {error}")
    
    if processor.warnings:
        print("\n⚠️ UYARILAR:")
        for warning in processor.warnings:
            print(f"  {warning}")
    
    # Özet bilgileri göster
    summary = processor.get_summary()
    print(f"\n📊 VERİ ÖZETİ:")
    print(f"  • Toplam satır: {summary['total_rows']}")
    print(f"  • Toplam sütun: {summary['total_columns']}")
    print(f"  • Hata sayısı: {summary['errors']}")
    print(f"  • Uyarı sayısı: {summary['warnings']}")
    
    # Temizlenmiş veriyi kaydet
    output_file = file_path.replace('.xlsx', '_temizlenmis.xlsx')
    processor.save_cleaned_data(output_file)
    
    return processor.df

if __name__ == "__main__":
    # Test için örnek dosyayı işle
    file_path = "sinav_bilgileri_sablon.xlsx"
    df = process_excel_file(file_path)
    
    if df is not None:
        print("\n📋 İlk 5 satır:")
        print(df.head().to_string())
        print(f"\n📈 Veri tipleri:")
        print(df.dtypes)
