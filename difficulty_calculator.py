import warnings

import mysql.connector
import pandas as pd
from sqlalchemy import create_engine

from database_config import DATABASE_CONFIG, get_connection_string

warnings.filterwarnings('ignore')

class DifficultyCalculator:
    """Sınav zorluk seviyesi hesaplama sınıfı"""
    
    def __init__(self):
        self.connection_string = get_connection_string()
        self.difficulty_rules = {
            'Çok Zor': {'akts_min': 7, 'sure_min': 120},
            'Zor': {'akts_min': 5, 'sure_min': 90},
            'Orta': {'akts_min': 3, 'sure_min': 60},
            'Kolay': {'akts_min': 0, 'sure_min': 0}
        }
    
    def get_course_akts_from_db(self):
        """Veritabanından ders AKTS bilgilerini alır"""
        print("⚠️ Veritabanı bağlantısı olmadığı için örnek AKTS değerleri kullanılacak")

        # Örnek AKTS değerleri
        sample_courses = {
            'kod': ['MAT101', 'FIZ201', 'BIL301', 'PRJ401', 'KIM101',
                   'MAT201', 'ELE301', 'END401', 'TUR101', 'ING201'],
            'ad': ['Matematik I', 'Fizik II', 'Bilgisayar Programlama', 'Bitirme Projesi', 'Genel Kimya',
                  'Matematik II', 'Elektronik', 'Endüstri Mühendisliği', 'Türk Dili', 'İngilizce II'],
            'akts': [6, 5, 4, 8, 5, 6, 5, 6, 2, 3],
            'bolum': ['Mühendislik', 'Mühendislik', 'Bilgisayar Müh.', 'Mühendislik', 'Mühendislik',
                     'Mühendislik', 'Elektrik Müh.', 'Endüstri Müh.', 'Genel', 'Genel']
        }

        df_courses = pd.DataFrame(sample_courses)
        print(f"✅ {len(df_courses)} ders için örnek AKTS bilgisi yüklendi")
        return df_courses
    
    def calculate_difficulty_level(self, akts, duration):
        """AKTS ve süre bilgisine göre zorluk seviyesi hesaplar"""
        
        # Çok Zor: AKTS ≥ 7 veya süre ≥ 120
        if akts >= 7 or duration >= 120:
            return 'Çok Zor'
        
        # Zor: AKTS ≥ 5 veya süre ≥ 90
        elif akts >= 5 or duration >= 90:
            return 'Zor'
        
        # Orta: AKTS ≥ 3 veya süre ≥ 60
        elif akts >= 3 or duration >= 60:
            return 'Orta'
        
        # Kolay: diğer
        else:
            return 'Kolay'
    
    def add_difficulty_to_exam_data(self, exam_df):
        """Sınav verilerine zorluk seviyesi ekler"""
        
        print("🎯 Zorluk seviyesi hesaplanıyor...")
        
        # Veritabanından ders bilgilerini al
        courses_df = self.get_course_akts_from_db()
        
        # Ders kodu ile AKTS bilgisini eşleştir
        exam_with_akts = exam_df.merge(
            courses_df[['kod', 'akts']], 
            left_on='Ders Kodu', 
            right_on='kod', 
            how='left'
        )
        
        # Eksik AKTS değerleri için uyarı
        missing_akts = exam_with_akts['akts'].isnull().sum()
        if missing_akts > 0:
            print(f"⚠️ {missing_akts} ders için AKTS bilgisi bulunamadı, varsayılan değer (3) kullanılacak")
            exam_with_akts['akts'] = exam_with_akts['akts'].fillna(3)
        
        # Zorluk seviyesini hesapla
        exam_with_akts['Zorluk Seviyesi'] = exam_with_akts.apply(
            lambda row: self.calculate_difficulty_level(row['akts'], row['Sınav Süresi']),
            axis=1
        )
        
        # Zorluk puanı ekle (planlama algoritması için)
        difficulty_scores = {'Çok Zor': 4, 'Zor': 3, 'Orta': 2, 'Kolay': 1}
        exam_with_akts['Zorluk Puanı'] = exam_with_akts['Zorluk Seviyesi'].map(difficulty_scores)
        
        # Gereksiz sütunu kaldır
        exam_with_akts = exam_with_akts.drop('kod', axis=1)
        
        print("✅ Zorluk seviyeleri hesaplandı")
        
        return exam_with_akts
    
    def get_difficulty_statistics(self, exam_df):
        """Zorluk seviyesi istatistiklerini döndürür"""
        
        if 'Zorluk Seviyesi' not in exam_df.columns:
            return None
        
        stats = {
            'difficulty_counts': exam_df['Zorluk Seviyesi'].value_counts().to_dict(),
            'difficulty_percentages': (exam_df['Zorluk Seviyesi'].value_counts(normalize=True) * 100).round(1).to_dict(),
            'avg_duration_by_difficulty': exam_df.groupby('Zorluk Seviyesi')['Sınav Süresi'].mean().round(1).to_dict(),
            'avg_akts_by_difficulty': exam_df.groupby('Zorluk Seviyesi')['akts'].mean().round(1).to_dict(),
            'avg_students_by_difficulty': exam_df.groupby('Zorluk Seviyesi')['Öğrenci Sayısı'].mean().round(1).to_dict()
        }
        
        return stats
    
    def print_difficulty_analysis(self, exam_df):
        """Zorluk analizi raporunu yazdırır"""
        
        if 'Zorluk Seviyesi' not in exam_df.columns:
            print("❌ Zorluk seviyesi hesaplanmamış")
            return
        
        stats = self.get_difficulty_statistics(exam_df)
        
        print("\n📊 ZORLUK SEVİYESİ ANALİZİ")
        print("=" * 50)
        
        print("\n🎯 Zorluk Dağılımı:")
        for difficulty, count in stats['difficulty_counts'].items():
            percentage = stats['difficulty_percentages'][difficulty]
            print(f"  • {difficulty}: {count} sınav (%{percentage})")
        
        print("\n⏱️ Ortalama Sınav Süreleri:")
        for difficulty, avg_duration in stats['avg_duration_by_difficulty'].items():
            print(f"  • {difficulty}: {avg_duration} dakika")
        
        print("\n📚 Ortalama AKTS Değerleri:")
        for difficulty, avg_akts in stats['avg_akts_by_difficulty'].items():
            print(f"  • {difficulty}: {avg_akts} AKTS")
        
        print("\n👥 Ortalama Öğrenci Sayıları:")
        for difficulty, avg_students in stats['avg_students_by_difficulty'].items():
            print(f"  • {difficulty}: {avg_students} öğrenci")
    
    def export_difficulty_report(self, exam_df, file_path):
        """Zorluk analizi raporunu dosyaya yazar"""
        
        if 'Zorluk Seviyesi' not in exam_df.columns:
            print("❌ Zorluk seviyesi hesaplanmamış")
            return False
        
        stats = self.get_difficulty_statistics(exam_df)
        
        report = []
        report.append("SINAV ZORLUK SEVİYESİ ANALİZ RAPORU")
        report.append("=" * 60)
        report.append(f"Tarih: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Toplam sınav sayısı: {len(exam_df)}")
        report.append("")
        
        report.append("ZORLUK HESAPLAMA KURALLARI:")
        report.append("• Çok Zor: AKTS ≥ 7 veya Süre ≥ 120 dakika")
        report.append("• Zor: AKTS ≥ 5 veya Süre ≥ 90 dakika")
        report.append("• Orta: AKTS ≥ 3 veya Süre ≥ 60 dakika")
        report.append("• Kolay: Diğer durumlar")
        report.append("")
        
        report.append("ZORLUK DAĞILIMI:")
        for difficulty, count in stats['difficulty_counts'].items():
            percentage = stats['difficulty_percentages'][difficulty]
            report.append(f"• {difficulty}: {count} sınav (%{percentage})")
        report.append("")
        
        report.append("ORTALAMA DEĞERLER:")
        report.append("Sınav Süreleri (dakika):")
        for difficulty, avg in stats['avg_duration_by_difficulty'].items():
            report.append(f"  • {difficulty}: {avg}")
        report.append("")
        
        report.append("AKTS Değerleri:")
        for difficulty, avg in stats['avg_akts_by_difficulty'].items():
            report.append(f"  • {difficulty}: {avg}")
        report.append("")
        
        report.append("Öğrenci Sayıları:")
        for difficulty, avg in stats['avg_students_by_difficulty'].items():
            report.append(f"  • {difficulty}: {avg}")
        report.append("")
        
        report.append("DETAYLI SINAV LİSTESİ:")
        for idx, row in exam_df.iterrows():
            report.append(f"{row['Ders Kodu']} - {row['Zorluk Seviyesi']} "
                         f"(AKTS: {row['akts']}, Süre: {row['Sınav Süresi']} dk, "
                         f"Öğrenci: {row['Öğrenci Sayısı']})")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"✅ Zorluk analizi raporu kaydedildi: {file_path}")
        return True

def process_exam_difficulty(excel_file_path):
    """Excel dosyasındaki sınav verilerini işler ve zorluk seviyesi ekler"""
    
    print("🎯 Sınav zorluk seviyesi hesaplama başlıyor...")
    
    try:
        # Excel dosyasını oku
        exam_df = pd.read_excel(excel_file_path)
        print(f"✅ Excel dosyası okundu: {len(exam_df)} sınav")
        
        # Zorluk hesaplayıcısını başlat
        calculator = DifficultyCalculator()
        
        # Zorluk seviyelerini hesapla
        exam_with_difficulty = calculator.add_difficulty_to_exam_data(exam_df)
        
        # Analiz raporunu yazdır
        calculator.print_difficulty_analysis(exam_with_difficulty)
        
        # Güncellenmiş veriyi kaydet
        output_file = excel_file_path.replace('.xlsx', '_zorluk_eklenmis.xlsx')
        exam_with_difficulty.to_excel(output_file, index=False)
        print(f"✅ Zorluk seviyeleri eklenerek kaydedildi: {output_file}")
        
        # Raporu kaydet
        calculator.export_difficulty_report(exam_with_difficulty, "zorluk_analizi_raporu.txt")
        
        return exam_with_difficulty
        
    except Exception as e:
        print(f"❌ Hata: {e}")
        return None

def show_difficulty_examples():
    """Zorluk seviyesi örneklerini gösterir"""
    
    print("\n📋 ZORLUK SEVİYESİ ÖRNEKLERİ:")
    print("=" * 40)
    
    examples = [
        {'ders': 'MAT101', 'akts': 6, 'sure': 90, 'beklenen': 'Zor'},
        {'ders': 'PRJ401', 'akts': 8, 'sure': 180, 'beklenen': 'Çok Zor'},
        {'ders': 'TUR101', 'akts': 2, 'sure': 60, 'beklenen': 'Orta'},
        {'ders': 'ING201', 'akts': 3, 'sure': 90, 'beklenen': 'Zor'},
        {'ders': 'TEST', 'akts': 2, 'sure': 45, 'beklenen': 'Kolay'}
    ]
    
    calculator = DifficultyCalculator()
    
    for example in examples:
        calculated = calculator.calculate_difficulty_level(example['akts'], example['sure'])
        status = "✅" if calculated == example['beklenen'] else "❌"
        print(f"{status} {example['ders']}: AKTS={example['akts']}, Süre={example['sure']}dk → {calculated}")

if __name__ == "__main__":
    # Zorluk seviyesi örneklerini göster
    show_difficulty_examples()
    
    # Temizlenmiş Excel dosyasını işle
    excel_file = "sinav_bilgileri_sablon_temizlenmis.xlsx"
    
    try:
        result_df = process_exam_difficulty(excel_file)
        if result_df is not None:
            print(f"\n📊 İşlem tamamlandı! {len(result_df)} sınav için zorluk seviyesi hesaplandı.")
    except FileNotFoundError:
        print(f"❌ Dosya bulunamadı: {excel_file}")
        print("⚠️ Önce excel_reader.py çalıştırarak temizlenmiş dosyayı oluşturun.")
