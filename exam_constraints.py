from datetime import datetime, time, timedelta
from typing import Dict, List, Tuple

import numpy as np
import pandas as pd


class ExamConstraints:
    """Sınav planlama kısıtları sınıfı"""
    
    def __init__(self):
        self.constraints = {
            'same_class_level_conflict': True,
            'difficult_exam_limit': True,
            'minimum_break_time': 15,  # dakika
            'lunch_break_forbidden': True,
            'friday_prayer_forbidden': True,
            'computer_classroom_required': True,
            'exam_week_only': True
        }
        
        # Yasak saatler
        self.forbidden_times = {
            'lunch_break': {'start': time(12, 15), 'end': time(13, 0)},
            'friday_prayer': {'start': time(12, 0), 'end': time(13, 30)}
        }
        
        # Sınav saatleri (günlük)
        self.exam_time_slots = [
            time(9, 0),   # 09:00
            time(11, 0),  # 11:00
            time(14, 0),  # 14:00
            time(16, 0)   # 16:00
        ]
        
        self.violations = []
    
    def validate_same_class_level_conflict(self, exam_schedule: pd.DataFrame) -> List[str]:
        """Kısıt 1: Aynı sınıf seviyesi dersleri aynı saatte sınav olamaz"""
        violations = []
        
        if exam_schedule.empty:
            return violations
        
        # Tarih ve saat bazında grupla
        for (date, time_slot), group in exam_schedule.groupby(['Tarih', 'Saat']):
            if len(group) > 1:
                # Aynı sınıf seviyesi kontrolü
                class_levels = group['Sınıf Seviyesi'].tolist()
                unique_levels = set(class_levels)
                
                if len(unique_levels) < len(class_levels):
                    # Aynı sınıf seviyesinden birden fazla sınav var
                    duplicate_levels = [level for level in unique_levels 
                                      if class_levels.count(level) > 1]
                    
                    for level in duplicate_levels:
                        conflicting_courses = group[group['Sınıf Seviyesi'] == level]['Ders Kodu'].tolist()
                        violations.append(
                            f"KISIT 1 İHLALİ: {date} {time_slot} saatinde {level} seviyesinde "
                            f"çakışan sınavlar: {', '.join(conflicting_courses)}"
                        )
        
        return violations
    
    def validate_difficult_exam_limit(self, exam_schedule: pd.DataFrame) -> List[str]:
        """Kısıt 2: Aynı gün iki zor/çok zor sınav yapılamaz"""
        violations = []
        
        if exam_schedule.empty:
            return violations
        
        # Tarih bazında grupla
        for date, group in exam_schedule.groupby('Tarih'):
            difficult_exams = group[group['Zorluk Seviyesi'].isin(['Zor', 'Çok Zor'])]
            
            if len(difficult_exams) > 1:
                course_codes = difficult_exams['Ders Kodu'].tolist()
                difficulty_levels = difficult_exams['Zorluk Seviyesi'].tolist()
                
                violations.append(
                    f"KISIT 2 İHLALİ: {date} tarihinde birden fazla zor sınav: "
                    f"{', '.join([f'{code}({diff})' for code, diff in zip(course_codes, difficulty_levels)])}"
                )
        
        return violations
    
    def validate_minimum_break_time(self, exam_schedule: pd.DataFrame) -> List[str]:
        """Kısıt 3: Aynı gün birden fazla sınav varsa aralarında 15 dakika boşluk"""
        violations = []
        
        if exam_schedule.empty:
            return violations
        
        # Tarih bazında grupla
        for date, group in exam_schedule.groupby('Tarih'):
            if len(group) > 1:
                # Saatlere göre sırala
                sorted_group = group.sort_values('Saat')
                
                for i in range(len(sorted_group) - 1):
                    current_exam = sorted_group.iloc[i]
                    next_exam = sorted_group.iloc[i + 1]
                    
                    # Mevcut sınavın bitiş saatini hesapla
                    current_start = datetime.strptime(str(current_exam['Saat']), '%H:%M:%S').time()
                    current_duration = int(current_exam['Sınav Süresi'])  # numpy.int64'ü int'e çevir

                    current_end_datetime = datetime.combine(datetime.today(), current_start) + timedelta(minutes=current_duration)
                    current_end = current_end_datetime.time()
                    
                    # Sonraki sınavın başlangıç saati
                    next_start = datetime.strptime(str(next_exam['Saat']), '%H:%M:%S').time()
                    
                    # Aralarındaki süreyi hesapla
                    time_diff = datetime.combine(datetime.today(), next_start) - datetime.combine(datetime.today(), current_end)
                    
                    if time_diff.total_seconds() < 15 * 60:  # 15 dakika = 900 saniye
                        violations.append(
                            f"KISIT 3 İHLALİ: {date} tarihinde {current_exam['Ders Kodu']} ve "
                            f"{next_exam['Ders Kodu']} arasında yeterli boşluk yok "
                            f"(Gerekli: 15 dk, Mevcut: {time_diff.total_seconds()/60:.0f} dk)"
                        )
        
        return violations
    
    def validate_lunch_break_forbidden(self, exam_schedule: pd.DataFrame) -> List[str]:
        """Kısıt 4: 12:15–13:00 arası sınav yapılamaz"""
        violations = []
        
        if exam_schedule.empty:
            return violations
        
        forbidden_start = self.forbidden_times['lunch_break']['start']
        forbidden_end = self.forbidden_times['lunch_break']['end']
        
        for _, exam in exam_schedule.iterrows():
            exam_start = datetime.strptime(str(exam['Saat']), '%H:%M:%S').time()
            exam_duration = int(exam['Sınav Süresi'])  # numpy.int64'ü int'e çevir

            exam_end_datetime = datetime.combine(datetime.today(), exam_start) + timedelta(minutes=exam_duration)
            exam_end = exam_end_datetime.time()
            
            # Sınav saatleri yasak aralıkla çakışıyor mu?
            if (exam_start < forbidden_end and exam_end > forbidden_start):
                violations.append(
                    f"KISIT 4 İHLALİ: {exam['Ders Kodu']} sınavı öğle arası yasak saatlerde "
                    f"({exam_start}-{exam_end}, Yasak: {forbidden_start}-{forbidden_end})"
                )
        
        return violations
    
    def validate_friday_prayer_forbidden(self, exam_schedule: pd.DataFrame) -> List[str]:
        """Kısıt 5: Cuma 12:00–13:30 arası sınav yapılamaz"""
        violations = []
        
        if exam_schedule.empty:
            return violations
        
        forbidden_start = self.forbidden_times['friday_prayer']['start']
        forbidden_end = self.forbidden_times['friday_prayer']['end']
        
        for _, exam in exam_schedule.iterrows():
            # Cuma günü kontrolü
            exam_date = pd.to_datetime(exam['Tarih'])
            if exam_date.weekday() == 4:  # Cuma = 4
                exam_start = datetime.strptime(str(exam['Saat']), '%H:%M:%S').time()
                exam_duration = int(exam['Sınav Süresi'])  # numpy.int64'ü int'e çevir

                exam_end_datetime = datetime.combine(datetime.today(), exam_start) + timedelta(minutes=exam_duration)
                exam_end = exam_end_datetime.time()
                
                # Sınav saatleri yasak aralıkla çakışıyor mu?
                if (exam_start < forbidden_end and exam_end > forbidden_start):
                    violations.append(
                        f"KISIT 5 İHLALİ: {exam['Ders Kodu']} sınavı Cuma günü namaz saatinde "
                        f"({exam_start}-{exam_end}, Yasak: {forbidden_start}-{forbidden_end})"
                    )
        
        return violations
    
    def validate_computer_classroom_required(self, exam_schedule: pd.DataFrame, classrooms_df: pd.DataFrame) -> List[str]:
        """Kısıt 6: Bilgisayar gerekiyorsa sadece bilgisayarlı derslik kullan"""
        violations = []
        
        if exam_schedule.empty:
            return violations
        
        for _, exam in exam_schedule.iterrows():
            if exam.get('Bilgisayar Gerekli', False):
                assigned_classroom = exam.get('Derslik', '')
                
                if assigned_classroom:
                    # Dersliğin bilgisayar özelliğini kontrol et
                    classroom_info = classrooms_df[classrooms_df['ad'] == assigned_classroom]
                    
                    if not classroom_info.empty:
                        has_computer = classroom_info.iloc[0]['bilgisayar_var_mi']
                        
                        if not has_computer:
                            violations.append(
                                f"KISIT 6 İHLALİ: {exam['Ders Kodu']} sınavı bilgisayar gerektiriyor "
                                f"ama {assigned_classroom} dersliğinde bilgisayar yok"
                            )
                    else:
                        violations.append(
                            f"KISIT 6 İHLALİ: {exam['Ders Kodu']} için atanan derslik {assigned_classroom} bulunamadı"
                        )
        
        return violations
    
    def validate_exam_week_only(self, exam_schedule: pd.DataFrame, exam_start_date: str, exam_end_date: str) -> List[str]:
        """Kısıt 7: Sınav haftası dışında tarih atanamaz"""
        violations = []
        
        if exam_schedule.empty:
            return violations
        
        start_date = pd.to_datetime(exam_start_date)
        end_date = pd.to_datetime(exam_end_date)
        
        for _, exam in exam_schedule.iterrows():
            exam_date = pd.to_datetime(exam['Tarih'])
            
            if exam_date < start_date or exam_date > end_date:
                violations.append(
                    f"KISIT 7 İHLALİ: {exam['Ders Kodu']} sınavı sınav haftası dışında "
                    f"({exam_date.strftime('%Y-%m-%d')}, Geçerli aralık: {exam_start_date} - {exam_end_date})"
                )
        
        return violations
    
    def validate_classroom_capacity(self, exam_schedule: pd.DataFrame, classrooms_df: pd.DataFrame) -> List[str]:
        """Ek Kısıt: Derslik kapasitesi kontrolü"""
        violations = []
        
        if exam_schedule.empty:
            return violations
        
        for _, exam in exam_schedule.iterrows():
            assigned_classroom = exam.get('Derslik', '')
            student_count = exam.get('Öğrenci Sayısı', 0)
            
            if assigned_classroom:
                classroom_info = classrooms_df[classrooms_df['ad'] == assigned_classroom]
                
                if not classroom_info.empty:
                    capacity = classroom_info.iloc[0]['kapasite']
                    
                    if student_count > capacity:
                        violations.append(
                            f"KAPASİTE İHLALİ: {exam['Ders Kodu']} sınavında {student_count} öğrenci "
                            f"ama {assigned_classroom} kapasitesi {capacity}"
                        )
        
        return violations
    
    def validate_all_constraints(self, exam_schedule: pd.DataFrame, classrooms_df: pd.DataFrame = None, 
                                exam_start_date: str = "2024-01-15", exam_end_date: str = "2024-01-30") -> Dict:
        """Tüm kısıtları kontrol eder"""
        
        all_violations = []
        constraint_results = {}
        
        # Kısıt 1: Aynı sınıf seviyesi çakışması
        violations_1 = self.validate_same_class_level_conflict(exam_schedule)
        all_violations.extend(violations_1)
        constraint_results['same_class_level'] = len(violations_1) == 0
        
        # Kısıt 2: Zor sınav limiti
        violations_2 = self.validate_difficult_exam_limit(exam_schedule)
        all_violations.extend(violations_2)
        constraint_results['difficult_exam_limit'] = len(violations_2) == 0
        
        # Kısıt 3: Minimum boşluk
        violations_3 = self.validate_minimum_break_time(exam_schedule)
        all_violations.extend(violations_3)
        constraint_results['minimum_break'] = len(violations_3) == 0
        
        # Kısıt 4: Öğle arası yasağı
        violations_4 = self.validate_lunch_break_forbidden(exam_schedule)
        all_violations.extend(violations_4)
        constraint_results['lunch_break'] = len(violations_4) == 0
        
        # Kısıt 5: Cuma namaz yasağı
        violations_5 = self.validate_friday_prayer_forbidden(exam_schedule)
        all_violations.extend(violations_5)
        constraint_results['friday_prayer'] = len(violations_5) == 0
        
        # Kısıt 6: Bilgisayar derslik
        if classrooms_df is not None:
            violations_6 = self.validate_computer_classroom_required(exam_schedule, classrooms_df)
            all_violations.extend(violations_6)
            constraint_results['computer_classroom'] = len(violations_6) == 0
            
            # Ek: Kapasite kontrolü
            violations_capacity = self.validate_classroom_capacity(exam_schedule, classrooms_df)
            all_violations.extend(violations_capacity)
            constraint_results['classroom_capacity'] = len(violations_capacity) == 0
        
        # Kısıt 7: Sınav haftası
        violations_7 = self.validate_exam_week_only(exam_schedule, exam_start_date, exam_end_date)
        all_violations.extend(violations_7)
        constraint_results['exam_week_only'] = len(violations_7) == 0
        
        return {
            'violations': all_violations,
            'constraint_results': constraint_results,
            'total_violations': len(all_violations),
            'is_valid': len(all_violations) == 0
        }
    
    def get_valid_time_slots(self, date: pd.Timestamp) -> List[time]:
        """Belirli bir tarih için geçerli saat dilimlerini döndürür"""
        valid_slots = []
        
        for time_slot in self.exam_time_slots:
            # Öğle arası kontrolü
            if (time_slot >= self.forbidden_times['lunch_break']['start'] and 
                time_slot < self.forbidden_times['lunch_break']['end']):
                continue
            
            # Cuma namaz saati kontrolü
            if (date.weekday() == 4 and  # Cuma
                time_slot >= self.forbidden_times['friday_prayer']['start'] and 
                time_slot < self.forbidden_times['friday_prayer']['end']):
                continue
            
            valid_slots.append(time_slot)
        
        return valid_slots
    
    def print_constraint_summary(self):
        """Kısıt özetini yazdırır"""
        print("\n📋 SINAV PLANLAMA KISITLARI")
        print("=" * 50)
        print("1. Aynı sınıf seviyesi dersleri aynı saatte sınav olamaz")
        print("2. Aynı gün iki zor/çok zor sınav yapılamaz")
        print("3. Aynı gün birden fazla sınav varsa aralarında 15 dakika boşluk")
        print("4. 12:15–13:00 arası sınav yapılamaz (öğle arası)")
        print("5. Cuma 12:00–13:30 arası sınav yapılamaz (namaz saati)")
        print("6. Bilgisayar gerekiyorsa sadece bilgisayarlı derslik kullan")
        print("7. Sınav haftası dışında tarih atanamaz")
        print("8. Derslik kapasitesi yeterli olmalı (ek kısıt)")
        
        print(f"\n⏰ Günlük sınav saatleri: {[str(t) for t in self.exam_time_slots]}")
        print(f"🚫 Yasak saatler:")
        print(f"   • Öğle arası: {self.forbidden_times['lunch_break']['start']}-{self.forbidden_times['lunch_break']['end']}")
        print(f"   • Cuma namaz: {self.forbidden_times['friday_prayer']['start']}-{self.forbidden_times['friday_prayer']['end']}")

if __name__ == "__main__":
    # Test için örnek kullanım
    constraints = ExamConstraints()
    constraints.print_constraint_summary()
    
    print("\n✅ Kısıt sınıfı başarıyla oluşturuldu!")
    print("📝 Bu sınıf OR-Tools optimizasyon algoritmasında kullanılacak.")
