"""
Veritabanı yapılandırma dosyası
MySQL bağlantı bilgilerini buradan güncelleyebilirsiniz
"""

# MySQL Bağlantı Ayarları
DATABASE_CONFIG = {
    'host': 'localhost',
    'user': 'root',  # MySQL kullanıcı adınızı buraya yazın
    'password': '',  # MySQL şifrenizi buraya yazın
    'database': 'sinav_programi',
    'charset': 'utf8mb4',
    'port': 3306
}

# SQLAlchemy bağlantı string'i
def get_connection_string():
    """SQLAlchemy için bağlantı string'i döndürür"""
    config = DATABASE_CONFIG
    return f"mysql+mysqlconnector://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}?charset={config['charset']}"

# Ta<PERSON><PERSON> yapılar<PERSON> (referans için)
TABLE_SCHEMAS = {
    'dersler': {
        'columns': ['id', 'kod', 'ad', 'akts', 'bolum', 'created_at'],
        'description': 'Ders bilgilerini saklar'
    },
    'sinavlar': {
        'columns': ['id', 'ders_kodu', 'ogretim_uyesi', 'ogrenci_sayisi', 'sure', 'bilgisayar_gerekli', 'created_at'],
        'description': 'Sınav bilgilerini saklar'
    },
    'sinav_tercihleri': {
        'columns': ['id', 'sinav_id', 'tercih1', 'tercih2', 'tercih3', 'created_at'],
        'description': 'Sınav tarih tercihlerini saklar'
    },
    'derslikler': {
        'columns': ['id', 'ad', 'kapasite', 'bolum', 'bilgisayar_var_mi', 'aktif', 'created_at'],
        'description': 'Derslik bilgilerini saklar'
    },
    'ayarlar': {
        'columns': ['id', 'sinav_baslangic_tarihi', 'sinav_bitis_tarihi', 'gunluk_sinav_saatleri', 'created_at', 'updated_at'],
        'description': 'Sistem ayarlarını saklar'
    }
}
